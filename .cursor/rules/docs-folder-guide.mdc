---
description: 
globs: 
alwaysApply: false
---
# docs 目录结构与用途说明

- [docs/](mdc:docs) 目录为项目的文档主目录，包含：
  - [.vitepress/](mdc:docs/.vitepress)：VitePress 配置、主题与自定义组件目录。
  - [examples/](mdc:docs/examples)：各组件的演示示例代码。
  - [components/](mdc:docs/components)：每个组件的 Markdown 文档说明。
  - [guide/](mdc:docs/guide)：项目开发、设计、安装等指南文档。
  - [index.md](mdc:docs/index.md)：文档首页。
  - 其他如 api-examples.md、markdown-examples.md 为 API 或 Markdown 语法示例。
- 该目录用于存放所有面向开发者和用户的文档内容，配合 VitePress 生成静态文档站点。
