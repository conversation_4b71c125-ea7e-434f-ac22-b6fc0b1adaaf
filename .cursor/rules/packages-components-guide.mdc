---
description: 
globs: 
alwaysApply: false
---
# packages/components 目录结构与用途说明

- [packages/components/](mdc:packages/components) 目录为主组件库源码目录。
- 主要内容包括：
  - [src/](mdc:packages/components/src)：各基础组件的源码目录，每个子目录对应一个组件（如 button、input、table 等）。
  - [components.ts](mdc:packages/components/components.ts)：导出所有组件的入口文件。
  - [index.ts](mdc:packages/components/index.ts)：组件库主入口，通常用于统一导出。
  - [auto-import-resolver.ts](mdc:packages/components/auto-import-resolver.ts)：自动导入相关的解析器实现。
  - [style/](mdc:packages/components/style)：组件库样式文件目录。
  - [vite.config.ts](mdc:packages/components/vite.config.ts)：组件库的 Vite 构建配置。
  - [tsconfig.json](mdc:packages/components/tsconfig.json)：TypeScript 配置文件。
  - [package.json](mdc:packages/components/package.json)：组件库包的依赖与元信息。
- 该目录用于开发、维护和构建所有 UI 组件，是项目的核心源码部分。
