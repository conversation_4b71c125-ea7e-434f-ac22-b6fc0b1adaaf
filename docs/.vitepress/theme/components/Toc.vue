<script setup lang="ts">
import { useData } from 'vitepress'
import { computed, onMounted, onUnmounted, ref } from 'vue'

const { page } = useData()

interface TocItem {
  level: number
  title: string
  link: string
  children?: TocItem[]
}

const tocItems = computed(() => {
  return page.value.headers || []
})

const activeAnchor = ref('')

// 监听滚动，高亮当前章节
onMounted(() => {
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          activeAnchor.value = entry.target.id
        }
      })
    },
    { threshold: 0.5 },
  )

  // 观察所有标题元素
  document.querySelectorAll('h1, h2, h3, h4, h5, h6').forEach((el) => {
    observer.observe(el)
  })

  onUnmounted(() => {
    observer.disconnect()
  })
})

function scrollToAnchor(anchor: string) {
  const element = document.getElementById(anchor)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}
</script>

<template>
  <nav v-if="tocItems.length" class="custom-toc">
    <div class="toc-header">
      <h3>目录</h3>
    </div>
    <ul class="toc-list">
      <li
        v-for="item in tocItems"
        :key="item.link"
        class="toc-item" :class="[
          `toc-level-${item.level}`,
          { 'toc-active': activeAnchor === item.link.slice(1) },
        ]"
      >
        <a
          :href="item.link"
          class="toc-link"
          @click.prevent="scrollToAnchor(item.link.slice(1))"
        >
          {{ item.title }}
        </a>
      </li>
    </ul>
  </nav>
</template>

<style scoped>
.custom-toc {
  background: var(--vp-c-bg-soft);
  border-radius: 8px;
  padding: 16px;
  border: 1px solid var(--vp-c-divider);
}

.toc-header h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 16px 0;
  color: var(--vp-c-text-1);
}

.toc-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.toc-item {
  margin: 4px 0;
}

.toc-link {
  display: block;
  padding: 6px 12px;
  border-radius: 6px;
  text-decoration: none;
  color: var(--vp-c-text-2);
  font-size: 14px;
  line-height: 1.4;
  transition: all 0.2s ease;
}

.toc-link:hover {
  color: var(--vp-c-text-1);
  background: var(--vp-c-bg-mute);
}

.toc-active .toc-link {
  color: var(--vp-c-brand);
  background: var(--vp-c-brand-soft);
  font-weight: 500;
}

.toc-level-1 .toc-link {
  font-weight: 600;
}

.toc-level-2 .toc-link {
  padding-left: 24px;
}

.toc-level-3 .toc-link {
  padding-left: 36px;
  font-size: 13px;
}

.toc-level-4 .toc-link {
  padding-left: 48px;
  font-size: 12px;
}
</style>
