<script setup lang="ts">
import { useData } from 'vitepress'
import DefaultTheme from 'vitepress/theme'
import Toc from '../components/Toc.vue'

// const { Toc } = DefaultTheme
</script>

<template>
  <div class="hrt-component-layout">
    <Content />
  </div>
</template>

<style lang="css">
.hrt-component-layout {
  padding: 0 48px 129px;
  & > div {
    & > div {
      padding-top: 64px;
      & > h1 {
        font-size: 32px;
        font-weight: 600;
        margin-bottom: 16px;
      }
      & > h2 {
        margin-top: 48px;
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 16px;
      }

      & > h3 {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 16px;
      }

      & > p {
        color: var(--hrt-color-neutral-200);
        margin-bottom: 16px;
        margin-top: 16px;
        line-height: 28px;
      }

      & > :not(pre) > code {
        border-radius: 4px;
        padding: 3px 6px;
        background-color: var(--vp-code-bg);
        transition:
          color 0.25s,
          background-color 0.5s;
      }

      & > :not(pre, h1, h2, h3, h4, h5, h6) > code {
        font-size: var(--vp-code-font-size);
        color: var(--vp-code-color);
      }

      & > table {
        width: 100%;
        border-collapse: collapse;
        border-spacing: 0;
        border: 1px solid var(--hrt-color-neutral-700);
        margin-bottom: 16px;

        thead {
          background-color: var(--hrt-color-neutral-700);
          th {
            padding: 12px;
            text-align: start;
          }
        }
        tbody {
          tr {
            td {
              padding: 12px;
            }
          }
        }
        code {
          border-radius: 4px;
          padding: 3px 6px;
          background-color: var(--vp-code-bg);
          transition:
            color 0.25s,
            background-color 0.5s;
          font-size: var(--vp-code-font-size);
          color: var(--vp-code-color);
        }
      }
    }
  }
}
</style>
