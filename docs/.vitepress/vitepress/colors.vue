<script setup lang="ts">
defineOptions({
  name: 'HrtColors',
})

const colors = [
  {
    name: '雾松绿',
    enName: 'fog-pine-green',
    colors: [
      '#edfcff',
      '#8cd6e6',
      '#3bb4cc',
      '#18a3bf',
      '#10849c',
      '#086578',
      '#034754',
    ],
  },
  {
    name: '蓝色',
    enName: 'blue',
    colors: [
      '#f2f6ff',
      '#9fbcf5',
      '#5285eb',
      '#2e6be6',
      '#1e52ba',
      '#113b8f',
      '#082663',
    ],
  },
  {
    name: '天蓝色',
    enName: 'azure',
    colors: [
      '#edf8ff',
      '#95cdf5',
      '#43a1e6',
      '#1c8dde',
      '#1271b5',
      '#0a568c',
      '#043c63',
    ],
  },
  {
    name: '午夜蓝',
    enName: 'midnight-blue',
    colors: [
      '#f2f3ff',
      '#a8ade0',
      '#6b72c2',
      '#5059b3',
      '#343c91',
      '#1e2570',
      '#0e1447',
    ],
  },
  {
    name: '棕黄色',
    enName: 'brownish-yellow',
    colors: [
      '#fffbf2',
      '#f5d89f',
      '#ebb852',
      '#e5a82e',
      '#ba861e',
      '#8f6511',
      '#634508',
    ],
  },
  {
    name: '红色',
    enName: 'red',
    colors: [
      '#fff2f3',
      '#f59fa6',
      '#eb525f',
      '#e52e3d',
      '#ba1e2b',
      '#8f111b',
      '#63080f',
    ],
  },
  {
    name: '橙色',
    enName: 'orange',
    colors: [
      '#fcf5f0',
      '#f2be99',
      '#e88b48',
      '#e37221',
      '#b85916',
      '#8c420d',
      '#612c06',
    ],
  },
  {
    name: '黄色',
    enName: 'yellow',
    colors: [
      '#fffceb',
      '#f5e69d',
      '#ebd254',
      '#e6c833',
      '#baa122',
      '#8f7a14',
      '#63550a',
    ],
  },
  {
    name: '紫色',
    enName: 'purple',
    colors: [
      '#f8f2ff',
      '#c39ff5',
      '#9152e8',
      '#7a2ee5',
      '#5f1eba',
      '#45118f',
      '#2e0863',
    ],
  },
  {
    name: '粉色',
    enName: 'pink',
    colors: [
      '#ffebfc',
      '#eb8ddb',
      '#d63cbd',
      '#cc19af',
      '#a6118e',
      '#800a6c',
      '#59054c',
    ],
  },
  {
    name: '绿色',
    enName: 'green',
    colors: [
      '#f3fff2',
      '#98e092',
      '#4ec244',
      '#2fb324',
      '#219117',
      '#15700d',
      '#0c4f06',
    ],
  },
  {
    name: '草绿色',
    enName: 'grass-green',
    colors: [
      '#fcffde',
      '#d6e68c',
      '#b4cc3b',
      '#a3bf18',
      '#82990f',
      '#617308',
      '#404d03',
    ],
  },
  {
    name: '中性色',
    enName: 'neutral',
    colors: [
      '#1f2e4d',
      '#3d4b66',
      '#7a8599',
      '#b8becc',
      '#dcdfe6',
      '#e8eaed',
      '#f7f8fa',
    ],
  },
]
</script>

<template>
  <div class="hrt-grid hrt-grid-cols-1 hrt-gap-4">
    <div v-for="color in colors" :key="color.name">
      <p class="hrt-text-lg hrt-font-bold hrt-m-0">
        {{ color.name }}/{{ color.enName }}
      </p>
      <div class="hrt-grid hrt-grid-cols-7">
        <div v-for="(c, index) in color.colors" :key="c" class="flex flex-col">
          <div
            class="hrt-w-full hrt-h-[100px]" :style="{
              backgroundColor: c,
              height: '100px',
              width: '100%',
            }"
          />
          <div class="hrt-text-sm">
            {{ (index + 1) * 100 }}
          </div>
          <div>
            {{ c }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
