<script setup lang="ts">
import { Warning } from '@element-plus/icons-vue'

defineProps({
  type: String,
  details: String,
})

</script>

<template>
  <span class="hrt-inline-flex hrt-items-center">
    <code class="api-typing hrt-mr-1">
      {{ type }}
    </code>
    <ClientOnly>
      <ElTooltip v-if="details" effect="light" trigger="click">
        <ElButton
          text
          :icon="Warning"
          class="hrt-p-2 hrt-text-4"
        />
        <template #content>
          <slot>
            <div class="hrt-m-1" style="max-width: 600px">
              <code
                style="
                  color: var(--code-tooltip-color);
                  background-color: var(--code-tooltip-bg-color);
                "
              >
                {{ details }}
              </code>
            </div>
          </slot>
        </template>
      </ElTooltip>
    </ClientOnly>
  </span>
</template>
