<script setup lang="ts">
defineOptions({
  name: 'HrtRadius',
})
</script>

<template>
  <div>
    <div class="hrt-grid hrt-grid-cols-1 hrt-gap-4">
      <p>圆角-4px：适合用在上层弹窗、文字</p>
      <div
        class="hrt-w-full hrt-h-36 hrt-rounded hrt-border"
        :style="{
          borderRadius: '4px',
          height: '100px',
          borderColor: '#DCDFE6',
        }"
      />
      <p>圆角-6px：适合用在底层信息卡片</p>
      <div
        class="hrt-w-full hrt-h-36 hrt-rounded hrt-border"
        :style="{
          borderRadius: '6px',
          height: '100px',
          borderColor: '#DCDFE6',
        }"
      />
    </div>
  </div>
</template>
