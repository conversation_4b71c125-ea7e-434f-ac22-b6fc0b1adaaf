/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    IRiCodeLine: typeof import('~icons/ri/code-line')['default']
    IRiFileCopyLine: typeof import('~icons/ri/file-copy-line')['default']
    IRiFlaskLine: typeof import('~icons/ri/flask-line')['default']
    IRiGithubLine: typeof import('~icons/ri/github-line')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    VpDemo: typeof import('./.vitepress/vitepress/components/vp-demo.vue')['default']
    VpSourceCode: typeof import('./.vitepress/vitepress/components/demo/vp-source-code.vue')['default']
  }
}
