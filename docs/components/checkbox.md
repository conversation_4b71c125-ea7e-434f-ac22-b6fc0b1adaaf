# Checkbox 多选框

在一组备选项中进行多选。

## 基本用法

单独使用可以表示两种状态之间的切换，写在标签中的内容为 checkbox 按钮后的介绍。

`checkbox-group` 元素能把多个 `checkbox` 管理为一组，只需要在 Group 中使用 `v-model` 绑定 Array 类型的变量即可。 只有一个选项时的默认值类型为 `Boolean`，当选中时值为 `true`。 `HrtCheckbox` 标签中的内容将成为复选框按钮之后的描述。

:::demo
checkbox/basic
:::

## 禁用状态

多选框不可用状态。

设置 `disabled` 属性即可。

:::demo
checkbox/disabled
:::

## 多选框组

适用于多个勾选框绑定到同一个数组的情景，通过是否勾选来表示这一组选项中选中的项。

在 `HrtCheckbox` 元素中定义 `v-model` 绑定变量，单一的 `checkbox` 中，默认绑定变量的值会是 `Boolean`，选中为 `true`。 在 `HrtCheckbox` 组件中，`value` 是选择框的值。 如果该组件下没有被传入内容，那么 `label` 将会作为 `checkbox` 按钮后的介绍。 `value` 也与数组中的元素值相对应。 如果指定的值存在于数组中，就处于选择状态，反之亦然。

:::demo
checkbox/group
:::

## 中间状态

`indeterminate` 属性用以表示 `checkbox` 的不确定状态，一般用于实现全选的效果。

:::demo
checkbox/indeterminate
:::

## 按钮样式

按钮样式的多选组合。

只需要把 `HrtCheckbox` 元素替换为 `HrtCheckboxButton` 元素即可。 此外，Element Plus 还提供了 `size` 属性。

:::demo
checkbox/button
:::

## 复合型多选框

根据需求的不同，提供不同的功能效果，包括下拉菜单、弹窗、时间等。

:::demo
checkbox/compound
:::

## HrtCheckbox API

### HrtCheckbox Attributes

| 属性名                | 说明                                                                                       | 类型                                       | 默认值 |
| --------------------- | ------------------------------------------------------------------------------------------ | ------------------------------------------ | ------ |
| model-value / v-model | 选中项绑定值                                                                               | `string` / `number` / `boolean`            | —      |
| value                 | 选中状态的值（仅有在 checkbox-group 或者绑定对象类型为 `array` 时有效）                    | `string` / `number` / `boolean` / `object` | —      |
| label                 | 选中状态的值，仅有在绑定对象类型为 `array` 时有效。如果没有 value，label 则作为 value 使用 | `string` / `number` / `boolean` / `object` | —      |
| true-value            | 选中时的值                                                                                 | `string` / `number`                        | —      |
| false-value           | 没有选中时的值                                                                             | `string` / `number`                        | —      |
| disabled              | 是否禁用                                                                                   | `boolean`                                  | false  |
| border                | 是否显示边框                                                                               | `boolean`                                  | false  |
| size                  | Checkbox 的尺寸                                                                            | `enum`                                     | —      |
| name                  | 原生 name 属性                                                                             | `string`                                   | —      |
| checked               | 当前是否勾选                                                                               | `boolean`                                  | false  |
| indeterminate         | 设置不确定状态，仅负责样式控制                                                             | `boolean`                                  | false  |
| validate-event        | 输入时是否触发表单的校验                                                                   | `boolean`                                  | true   |
| tabindex              | 输入框的 `tabindex`                                                                        | `string` / `number`                        | —      |
| id                    | input id                                                                                   | `string`                                   | —      |
| aria-controls         | 与 `aria-control` 一致，当 `indeterminate` 为 `true` 时生效                                | `string`                                   | —      |

## HrtCheckbox Events

| 事件名 | 说明                     | 类型                                           |
| ------ | ------------------------ | ---------------------------------------------- |
| change | 当绑定值变化时触发的事件 | `(value: string \| number \| boolean) => void` |

## HrtCheckbox Slots

| 插槽名  | 说明           |
| ------- | -------------- |
| default | 自定义默认内容 |

## HrtCheckboxGroup API

### HrtCheckboxGroup Attributes

| 属性名                | 说明                                | 类型                              | 默认值    |
| --------------------- | ----------------------------------- | --------------------------------- | --------- |
| model-value / v-model | 绑定值                              | `string[] \| number[]`            | `[]`      |
| size                  | 多选框组尺寸                        | `'large' \| 'default' \| 'small'` | —         |
| disabled              | 是否禁用                            | `boolean`                         | `false`   |
| min                   | 可被勾选的 `HrtCheckbox` 的最小数量 | `number`                          | —         |
| max                   | 可被勾选的 `HrtCheckbox` 的最大数量 | `number`                          | —         |
| aria-label            | 原生 `aria-label` 属性              | `string`                          | —         |
| text-color            | 当按钮为活跃状态时的字体颜色        | `string`                          | `#ffffff` |
| fill                  | 当按钮为活跃状态时的边框和背景颜色  | `string`                          | `#409eff` |
| tag                   | 复选框组元素标签                    | `string`                          | `div`     |
| validate-event        | 是否触发表单验证                    | `boolean`                         | `true`    |

## HrtCheckboxGroup Events

| 事件名 | 说明                     | 类型                                    |
| ------ | ------------------------ | --------------------------------------- |
| change | 当绑定值变化时触发的事件 | `(value: string[] \| number[]) => void` |

## HrtCheckboxGroup Slots

| 插槽名  | 说明           | 子标签                          |
| ------- | -------------- | ------------------------------- |
| default | 自定义默认内容 | HrtCheckbox / HrtCheckboxButton |

## HrtCheckboxButton API

### HrtCheckboxButton Attributes

| 名称        | 详情                                                                                     | 类型                                       | 默认值  |
| ----------- | ---------------------------------------------------------------------------------------- | ------------------------------------------ | ------- |
| value       | 选中状态的值，只有在绑定对象类型为 array 时有效                                          | `string` / `number` / `boolean` / `object` | —       |
| label       | 选中状态的值，只有在绑定对象类型为 array 时有效。如果没有 value，label 则作为 value 使用 | `string` / `number` / `boolean` / `object` | —       |
| true-value  | 选中时的值                                                                               | `string` / `number`                        | —       |
| false-value | 没有选中时的值                                                                           | `string` / `number`                        | —       |
| disabled    | 是否禁用                                                                                 | `boolean`                                  | `false` |
| name        | 原生 name 属性                                                                           | `string`                                   | —       |
| checked     | 当前是否勾选                                                                             | `boolean`                                  | `false` |
| outline     | 是否为 outline 样式                                                                      | `boolean`                                  | `true`  |

## HrtCheckboxButton Events

| 事件名    | 说明                     | 类型                                           |
| --------- | ------------------------ | ---------------------------------------------- |
| change    | 当绑定值变化时触发的事件 | `(value: string \| number \| boolean) => void` |
| iconClick | 当点击图标时触发的事件   | `() => void`                                   |

## HrtCheckboxButton Slots

| 插槽名  | 说明           |
| ------- | -------------- |
| default | 自定义默认内容 |
| icon    | 自定义图标     |
