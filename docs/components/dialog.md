# Dialog 对话框

在保留当前页面状态的情况下，告知用户并承载相关操作。

## 基础用法

Dialog 弹出一个对话框，适合需要定制性更大的场景。

需要设置 `model-value / v-model` 属性，它接收 `Boolean`，当为 `true` 时显示 Dialog。 Dialog 分为两个部分：`body` 和 `footer`，`footer` 需要具名为 `footer` 的 `slot`。 `title` 属性用于定义标题，它是可选的，默认值为空。

:::demo
dialog/basic
:::

## 对话框尺寸

不同尺寸的对话框适用环境不同

| 标题     | 尺寸      | 适用于                                             |
| -------- | --------- | -------------------------------------------------- |
| 超大弹窗 | `800*560` | 表单填写类弹窗，内容展示类弹窗                     |
| 大弹窗   | `600*400` | 表单填写类弹窗、内容展示类弹窗、对话框、宣传类弹窗 |
| 中弹窗   | `400*240` | 表单填写类弹窗、内容展示类弹窗、宣传类弹窗         |
| 小弹窗   | `320*160` | 信息确认类弹窗                                     |

:::demo
dialog/size
:::

## API

### Props

| 属性名                | 说明            | 类型                   | 默认 |
| --------------------- | --------------- | ---------------------- | ---- |
| model-value / v-model | 是否显示 Dialog | `boolean`              | —    |
| size                  | Dialog尺寸      | `xs \| md \| lg \| xl` | `lg` |

其他属性参考Element-Plus [Dialog](https://element-plus.org/zh-CN/component/dialog.html#attributes)

## 贡献者

| 责任人 | 贡献者 |
| ------ | ------ |
| 刘浪   | 刘浪   |
