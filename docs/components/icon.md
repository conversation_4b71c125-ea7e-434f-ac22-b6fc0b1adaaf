# Icon 图标

`@hrt/icons` 提供了一套常用的图标集合。

## 安装

### 使用包管理器

::: code-group

```bash [pnpm]
$ pnpm add @hrt/icons
```

```bash [npm]
$ npm install @hrt/icons
```

```bash [yarn]
$ yarn add @hrt/icons
```

:::

### 注册所有图标

```ts main.ts
import HrtIcons from '@hrt/icons'
import { createApp } from 'vue'

const app = createApp()
app
  .use(HrtIcons)
  .mount('#app')
```

### 引入组件

:::demo sad
icon/basic
:::

## 直接使用 SVG 图标

```vue
<template>
  <div style="font-size: 20px">
    <!-- 由于SVG图标默认不携带任何属性 -->
    <!-- 你需要直接提供它们 -->
    <Edit style="width: 1em; height: 1em; margin-right: 8px" />
    <Share style="width: 1em; height: 1em; margin-right: 8px" />
    <Delete style="width: 1em; height: 1em; margin-right: 8px" />
    <Search style="width: 1em; height: 1em; margin-right: 8px" />
  </div>
</template>
```

<HrtBriefcaseLine />
