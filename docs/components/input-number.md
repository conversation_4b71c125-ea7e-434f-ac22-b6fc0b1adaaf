# InputNumber 数字输入框

用于在表单中接收数字输入，支持步进、限制范围等功能。

## 基础用法

要使用它，只需要在 `HrtInputNumber` 元素中使用 `v-model` 绑定变量即可，变量的初始值即为默认值。

:::demo
input-number/basic
:::

:::tip
当输入无效的字符串到输入框时，由于错误，输入值将把 NaN 导入到上层
:::

## 禁用状态

disabled属性接受一个 `Boolean`，设置为 `true` 即可禁用整个组件。
如果你只需要控制数值在某一范围内，可以设置 `min` 属性和 `max` 属性， 默认最小值为 `0`。

:::demo
input-number/disabled
:::

## 步进

允许定义递增递减的步进控制

设置 `step` 属性可以控制步长。

:::demo
input-number/step
:::

## 精度

设置 `precision` 属性可以控制数值精度，接收一个 `Number`。

:::demo
input-number/precision
:::

:::tip
`precision` 的值必须是一个非负整数，并且不能小于 `step` 的小数位数。
:::

## 按钮位置

设置 `controls-position` 属性可以控制按钮位置。

:::demo
input-number/controls-position
:::

## 属性

| 属性名            | 说明     | 类型                   | 默认值      |
| ----------------- | -------- | ---------------------- | ----------- |
| modelValue        | 绑定值   | `number`               | --          |
| min               | 最小值   | `number`               | `-Infinity` |
| max               | 最大值   | `number`               | `Infinity`  |
| step              | 步长     | `number`               | `1`         |
| disabled          | 是否禁用 | `boolean`              | `false`     |
| readonly          | 是否只读 | `boolean`              | `false`     |
| precision         | 数值精度 | `number`               | --          |
| controls-position | 按钮位置 | `'right' \| 'between'` | `'right'`   |
| name              | 名称     | `string`               | --          |
| stepStrictly      | 严格步进 | `boolean`              | `false`     |

## 事件

| 事件名 | 说明           | 回调参数                                             |
| ------ | -------------- | ---------------------------------------------------- |
| change | 数值变化时触发 | `(currentValue?: number, oldValue?: number) => void` |
| blur   | 失去焦点时触发 | `(event: FocusEvent) => void`                        |
| focus  | 获得焦点时触发 | `(event: FocusEvent) => void`                        |

## 贡献者

| 责任人 | 贡献者 |
| ------ | ------ |
| 刘浪   | 刘浪   |
