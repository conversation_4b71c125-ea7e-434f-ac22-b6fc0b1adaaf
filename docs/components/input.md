# Input 输入框

通过鼠标或键盘输入字符

## 基础用法

:::demo
input/basic
:::

## 禁用状态

通过 `disabled` 属性指定是否禁用 input 组件

:::demo
input/disabled
:::

## 尺寸

高度固定，默认提供5种宽度尺寸，按照文本内容选择合适的尺寸

:::demo
input/size
:::

## 带有图标

:::demo
input/icon
:::

## 带有搜索按钮

:::demo
input/search
:::

## 文本域

用于输入多行文本信息可缩放的输入框。 添加 `type="textarea"` 属性来将 `input` 元素转换为原生的 `textarea` 元素。

文本域高度可通过 `rows` 属性控制

:::demo
input/textarea
:::

## 输入长度限制

使用 `maxlength` 和 `minlength` 属性, 来控制输入内容的最大字数和最小字数。 "字符数"使用JavaScript字符串长度来衡量。 为文本或文本输入类型设置 `maxlength` prop可以限制输入值的长度。 允许你通过设置 `show-word-limit` 到 `true` 来显示剩余字数。

:::demo
input/length
:::

## API

### Props

| 属性名                | 说明                                                                                                           | 类型                                                  | 默认值  |
| --------------------- | -------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------- | ------- |
| type                  | 类型                                                                                                           | `string`                                              | `text`  |
| model-value / v-model | 绑定值                                                                                                         | `string`/ `number`                                    | —       |
| maxlength             | 同原生 maxlength 属性                                                                                          | `string`/`number`                                     | —       |
| minlength             | 原生属性，最小输入长度                                                                                         | `string`/`number`                                     | —       |
| show-word-limit       | 是否显示统计字数, 只在 `type` 为 'text' 或 'textarea' 的时候生效                                               | `boolean`                                             | `false` |
| placeholder           | 输入框占位文本                                                                                                 | `string`                                              | —       |
| clearable             | 是否显示清除按钮，只有当 `type` 不是 textarea时生效                                                            | `boolean`                                             | `false` |
| formatter             | 指定输入值的格式。(只有当 `type` 是"text"时才能工作)                                                           | `Function`                                            | —       |
| parser                | 指定从格式化器输入中提取的值。(仅当 `type` 是"text"时才起作用)                                                 | `Function`                                            | —       |
| show-password         | 是否显示切换密码图标                                                                                           | `boolean`                                             | `false` |
| disabled              | 是否禁用                                                                                                       | `boolean`                                             | `false` |
| size                  | 输入框尺寸，只在 `type` 不为 'textarea' 时有效                                                                 | `'mini' \| 'small' \| 'middle' \| 'large' \| 'super'` | `large` |
| prefix-icon           | 自定义前缀图标                                                                                                 | `string`/`Component`                                  | —       |
| suffix-icon           | 自定义后缀图标                                                                                                 | `string`/`Component`                                  | —       |
| rows                  | 输入框行数，仅 `type` 为 'textarea' 时有效                                                                     | `number`                                              | 2       |
| autosize              | textarea 高度是否自适应，仅 `type` 为 'textarea' 时生效。 可以接受一个对象，比如: `{ minRows: 2, maxRows: 6 }` | `boolean`/`object`                                    | `false` |
| autocomplete          | 原生 `autocomplete` 属性                                                                                       | `string`                                              | `off`   |
| name                  | 等价于原生 input `name` 属性                                                                                   | `string`                                              | —       |
| readonly              | 原生 `readonly` 属性，是否只读                                                                                 | `boolean`                                             | `false` |
| max                   | 原生 `max` 属性，设置最大值                                                                                    | —                                                     | —       |
| min                   | 原生属性，设置最小值                                                                                           | —                                                     | —       |
| step                  | 原生属性，设置输入字段的合法数字间隔                                                                           | —                                                     | —       |
| resize                | 控制是否能被用户缩放                                                                                           | `'none' \| 'both' \|'horizontal' \| 'vertical'`       | —       |
| autofocus             | 原生属性，自动获取焦点                                                                                         | `boolean`                                             | `false` |
| form                  | 原生属性                                                                                                       | `string`                                              | —       |
| aria-label            | 等价于原生 input `aria-label` 属性                                                                             | `string`                                              | —       |
| tabindex              | 输入框的 `tabindex`                                                                                            | `string`/`number`                                     | —       |
| validate-event        | 输入时是否触发表单的校验                                                                                       | `boolean`                                             | `true`  |
| input-style           | input 元素或 textarea 元素的 style                                                                             | `string`/`object`                                     | `{}`    |
| showSuffixBg          | 是否展示后缀背景                                                                                               | `boolean`                                             | `false` |

### 事件

| 事件名 | 说明                                                          | 类型                                |
| ------ | ------------------------------------------------------------- | ----------------------------------- |
| blur   | 当选择器的输入框失去焦点时触发                                | `(event: FocusEvent) => void`       |
| focus  | 当选择器的输入框获得焦点时触发                                | `(event: FocusEvent) => void`       |
| change | 仅当 `modelValue` 改变时，当输入框失去焦点或用户按Enter时触发 | `(value: string \| number) => void` |
| input  | 在 HrtInput 值改变时触发                                      | `(value: string \| number) => void` |
| clear  | 在点击由 `clearable` 属性生成的清空按钮时触发                 | `() => void`                        |
