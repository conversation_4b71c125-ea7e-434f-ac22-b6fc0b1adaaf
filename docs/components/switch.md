# Switch 开关

表示两种相互对立的状态间的切换，多用于触发「开/关」。

## 基础用法

绑定 `v-model` 到一个 `Boolean` 类型的变量。 可以使用 `--hrt-color-blue` 属性与 `--hrt-color-neutral-400` 属性来设置开关的背景色。

:::demo
switch/basic
:::

## 尺寸

支持三种尺寸：`large`、`default`、`small`。

:::demo
switch/size
:::

## 状态

可通过 `disabled` 属性设置开关的禁用状态。

:::demo
switch/status
:::

## 带标识

可通过 `active-text` 和 `inactive-text` 属性设置开关的标识。

:::demo
switch/with-identification
:::

## 自定义值

可通过 `active-value` 和 `inactive-value` 属性设置开关的值。

:::demo
switch/custom-value
:::

## API

### 属性

| 属性名             | 说明         | 类型                            | 默认值      |
| ------------------ | ------------ | ------------------------------- | ----------- |
| modelValue/v-model | 绑定值       | `boolean` / `string` / `number` | `false`     |
| activeText         | 打开时的文字 | `string`                        | --          |
| inactiveText       | 关闭时的文字 | `string`                        | --          |
| activeValue        | 打开时的值   | `boolean` / `string` / `number` | `true`      |
| inactiveValue      | 关闭时的值   | `boolean` / `string` / `number` | `false`     |
| disabled           | 是否禁用     | `boolean`                       | `false`     |
| size               | 尺寸         | `string`                        | `'default'` |

### 事件

| 事件名 | 说明         | 类型                                         |
| ------ | ------------ | -------------------------------------------- |
| change | 值改变时触发 | `(value: boolean / string / number) => void` |

## 贡献者

| 责任人 | 贡献者 |
| ------ | ------ |
| 刘浪   | 刘浪   |
