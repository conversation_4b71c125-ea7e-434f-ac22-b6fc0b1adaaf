# Tabs 标签页

## 基本用法

:::demo
tabs/basic
:::

## 卡片样式

:::demo
tabs/card
:::

## 图标样式

:::demo
tabs/icon
:::

## 自定义标签页

:::demo
tabs/custom-label
:::

## 标签页翻页

可通过 `pageTurning` 属性开启标签页翻页，开启后标签页会显示翻页按钮，点击翻页按钮可以切换到上一个或下一个未被禁用的标签页。

:::demo
tabs/page
:::

## 可关闭标签页

:::demo
tabs/closeable
:::

## API

### HrtTabs Props

| 属性              | 说明               | 类型                | 默认值    |
| ----------------- | ------------------ | ------------------- | --------- |
| v-model           | 当前激活的标签页   | `string`            | --        |
| type              | 标签页类型         | `'basic' \| 'card'` | `'basic'` |
| pageTurning       | 是否开启标签页翻页 | `boolean`           | `false`   |
| pageTurningNumber | 标签页翻页数量     | `number`            | `3`       |
| closable          | 是否可关闭标签页   | `boolean`           | `false`   |

### HrtTabs Slot

| 名称  | 说明       |
| ----- | ---------- |
| label | 标签页导航 |

### HrtTabPane Props

| 属性      | 说明             | 类型      | 默认值  |
| --------- | ---------------- | --------- | ------- |
| label     | 标签页标题       | `string`  | --      |
| name      | 标签页名称       | `string`  | --      |
| disabled  | 是否禁用标签页   | `boolean` | `false` |
| closeable | 是否可关闭标签页 | `boolean` | `false` |

### HrtTabPane Slot

| 名称    | 说明       |
| ------- | ---------- |
| default | 标签页内容 |
