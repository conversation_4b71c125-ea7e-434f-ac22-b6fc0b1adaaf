# Text 文本

文本的常见操作

## 基础用法

由 `type` 属性来选择 Text 的类型。

:::demo
text/basic
:::

## 标题

展示不同级别的标题。

:::demo
text/title
:::

## 省略

通过 `truncated` 属性，在文本超过视图或最大宽度设置时展示省略符。

通过 `line-clamp` 属性控制多行的样式。

:::demo
text/truncated
:::

## 覆盖

通过 `tag` 属性可以自定义文本标签。

:::demo
text/tag
:::

## API

### Text Props

| 名称       | 类型                                                                     | 默认值      | 说明       |
| ---------- | ------------------------------------------------------------------------ | ----------- | ---------- |
| type       | `'primary' \| 'success' \| 'warning' \| 'danger' \| 'info' \| 'default'` | `default`   | 文本类型   |
| tag        | `string`                                                                 | `span`      | 自定义标签 |
| line-clamp | `number`                                                                 | `undefined` | 文本行数   |
| truncated  | `boolean`                                                                | `false`     | 是否省略   |

### Title Props

| 名称       | 类型      | 默认值      | 说明     |
| ---------- | --------- | ----------- | -------- |
| size       | `xl`      | `base`      | 标题大小 |
| line-clamp | `number`  | `undefined` | 文本行数 |
| truncated  | `boolean` | `undefined` | 是否省略 |

### Slots

| 名称    | 说明     |
| ------- | -------- |
| default | 默认内容 |
