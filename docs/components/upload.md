# Upload 上传

通过点击或者拖拽上传文件。

## 基础用法

:::demo 通过 `slot` 你可以传入自定义的上传按钮类型。
upload/slot
:::

## 覆盖前一个文件

:::demo 设置 `multiple` 可以在选中时自动替换上一个文件。
upload/cover
:::

## 用户头像

:::demo
upload/avatar
:::

## 类型

:::demo 使用 `list-type` 属性来设定文件列表的样式，请注意当list-type设置为 `picture`、`picture-card` 时将只允许上传图片
upload/type
:::

## 七牛云上传

:::demo 设置 `auto-upload` 可以通过七牛云上传文件，请注意当auto-upload设置为 `true` 时将自动上传文件至七牛云且 `qiniu-token`、`qiniu-domain`必须传入，由于demo没有合适的`qiniu-token`、`qiniu-domain`，所以demo可能会报错
upload/qiniu
:::

## API

### Props

| 参数         | 说明                                                            | 类型                                        | 默认值             |
| ------------ | --------------------------------------------------------------- | ------------------------------------------- | ------------------ |
| `v-model`    | 受控上传文件                                                    | `HrtUploadModelValue`，如[{ url: 'xxx.jpg', name: 'xxx' }]                       | `[]`               |
| `multiple`   | 是否支持多个文件                                                | `boolean`                                   | `true`             |
| `drag`       | 是否启用拖拽上传                                                | `boolean`                                   | `false`            |
| `disabled`   | 是否禁用上传                                                    | `boolean`                                   | `false`            |
| `file-types` | 文件类型限制                                                    | `string[]`，如['jpg', 'jpeg', 'png', 'pdf'] | `[]`               |
| `tip`        | 提示，可以用于提示文件限制格式或者文件限制大小                  | `string`                                    |                    |
| `limit`      | 允许上传文件的最大数量                                          | `number`                                    | `10`               |
| `limit-size` | 允许单个上传文件的大小                                          | `number`                                    | `50 * 1024 * 1024` |
| `list-type`  | 文件列表的类型，当值为`picture`、`picture-card`时，只能上传图片 | `'text' \| 'picture' \| 'picture-card'`     | `text`              |
| `auto-upload` | 是否自动上传文件                                          | `boolean`                                    | `true` |
| `show-file-list` | 是否显示已上传文件列表                                          | `boolean`                                    | `true` |
| `qiniu-token` | 七牛上传需要的token，autoUpload为true时必传                                          | `string`                                    |  |
| `qiniu-domain` | 七牛域名，autoUpload为true时必传                                          | `string`                                    |  |

### Events

| 事件名      | 说明             | 类型 |
| --------- | ---------------- | ---- |
| `qiniu-upload-error` | 七牛上传失败时触发该事件 | -    |

### Slots

| 插槽名      | 说明             | 类型 |
| --------- | ---------------- | ---- |
| `default` | 自定义的上传按钮 | -    |


