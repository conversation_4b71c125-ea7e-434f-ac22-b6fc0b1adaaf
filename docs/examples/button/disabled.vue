<script setup lang="ts">
import { HrtButton } from '@hrt/components'
</script>

<template>
  <div>
    <HrtButton disabled>
      Default
    </HrtButton>
    <HrtButton type="primary" disabled>
      Primary
    </HrtButton>
    <HrtButton type="success" disabled>
      Success
    </HrtButton>
    <HrtButton type="info" disabled>
      Info
    </HrtButton>
    <HrtButton type="warning" disabled>
      Warning
    </HrtButton>
    <HrtButton type="danger" disabled>
      Danger
    </HrtButton>
    <HrtButton disabled dashed>
      虚线按钮
    </HrtButton>
  </div>
</template>
