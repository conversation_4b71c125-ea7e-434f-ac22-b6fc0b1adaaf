<script setup lang="ts">
import { HrtCheckbox } from '@hrt/components'
import { ref } from 'vue'

const modelValue = ref(false)
const modelValue2 = ref(false)

const checked3 = ref(false)
const checked4 = ref(false)
const checked5 = ref(false)
const checked6 = ref(false)
</script>

<template>
  <HrtCheckbox v-model="modelValue" label="选项1" />
  <HrtCheckbox v-model="modelValue2" label="选项2" />
  <div class="hrt-my-2">
    <HrtCheckbox v-model="checked3" label="选项3" />
    <HrtCheckbox v-model="checked4" label="选项4" />
  </div>
  <div>
    <HrtCheckbox v-model="checked5" label="选项5" />
    <HrtCheckbox v-model="checked6" label="选项6" />
  </div>
</template>
