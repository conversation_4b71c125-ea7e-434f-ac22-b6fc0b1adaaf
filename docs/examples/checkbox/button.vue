<script setup lang="ts">
import { HrtCheckboxButton, HrtCheckboxGroup } from '@hrt/components'
import { ref } from 'vue'

const modelValue = ref(['value1', 'value5'])
</script>

<template>
  <div>
    当前选中值：{{ modelValue }}
  </div>
  <HrtCheckboxGroup v-model="modelValue" class="hrt-mt-4">
    <HrtCheckboxButton label="选项1" value="value1" />
    <HrtCheckboxButton label="选项2" value="value2" />
    <HrtCheckboxButton label="选项3" value="value3" />
    <HrtCheckboxButton label="选项4" value="value4" disabled />
    <HrtCheckboxButton label="选项5" value="value5" disabled />
  </HrtCheckboxGroup>
  <HrtCheckboxGroup v-model="modelValue" class="hrt-mt-4">
    <HrtCheckboxButton label="选项1" value="value1" :outline="false" />
    <HrtCheckboxButton label="选项2" value="value2" :outline="false" />
    <HrtCheckboxButton label="选项3" value="value3" :outline="false" />
    <HrtCheckboxButton label="选项4" value="value4" :outline="false" />
    <HrtCheckboxButton label="选项5" value="value5" disabled :outline="false" />
  </HrtCheckboxGroup>
</template>
