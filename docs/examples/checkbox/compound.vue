<script setup lang="ts">
import { EditPen } from '@element-plus/icons-vue'
import { HrtCheckboxButton, HrtCheckboxGroup } from '@hrt/components'
import { ElIcon } from 'element-plus'
import { ref } from 'vue'

const modelValue = ref(['value1', 'value5'])
</script>

<template>
  <div>
    当前选中值：{{ modelValue }}
  </div>
  <HrtCheckboxGroup v-model="modelValue" class="hrt-mt-4">
    <HrtCheckboxButton label="选项1" value="value1">
      <template #icon>
        <ElIcon class="hrt-size-4">
          <EditPen />
        </ElIcon>
      </template>
    </HrtCheckboxButton>
    <HrtCheckboxButton label="选项2" value="value2" />
    <HrtCheckboxButton label="选项3" value="value3" />
    <HrtCheckboxButton label="选项4" value="value4" disabled>
      <template #icon>
        <ElIcon class="hrt-size-4">
          <EditPen />
        </ElIcon>
      </template>
    </HrtCheckboxButton>
    <HrtCheckboxButton label="选项5" value="value5" disabled>
      <template #icon>
        <ElIcon class="hrt-size-4">
          <EditPen />
        </ElIcon>
      </template>
    </HrtCheckboxButton>
  </HrtCheckboxGroup>
  <HrtCheckboxGroup v-model="modelValue" class="hrt-mt-4">
    <HrtCheckboxButton label="选项1" value="value1" :outline="false" />
    <HrtCheckboxButton label="选项2" value="value2" :outline="false" />
    <HrtCheckboxButton label="选项3" value="value3" :outline="false" />
    <HrtCheckboxButton label="选项4" value="value4" :outline="false" />
    <HrtCheckboxButton label="选项5" value="value5" disabled :outline="false" />
  </HrtCheckboxGroup>
</template>
