<script setup lang="ts">
import { HrtCheckbox } from '@hrt/components'
import { ref } from 'vue'

const modelValue = ref(false)
const modelValue2 = ref(true)
const modelValue3 = ref(false)
</script>

<template>
  <HrtCheckbox v-model="modelValue" disabled label="Disabled" />
  <HrtCheckbox v-model="modelValue2" disabled label="Selected Disabled" />
  <HrtCheckbox v-model="modelValue3" label="未禁用" />
</template>
