<script setup lang="ts">
import { HrtCheckbox, HrtCheckboxGroup } from '@hrt/components'
import { computed, ref } from 'vue'

const modelValue = ref(['value1', 'value2', 'value5'])
const checkAll = ref(false)
const indeterminate = computed(() => {
  return modelValue.value.length > 0 && modelValue.value.length < 5
})

function handleCheckAllChange(value: boolean | string | number) {
  if (value) {
    modelValue.value = ['value1', 'value2', 'value3', 'value4', 'value5']
  }
  else {
    modelValue.value = []
  }
}
</script>

<template>
  <div>
    当前选中值：{{ modelValue }}
  </div>
  <HrtCheckbox
    v-model="checkAll"
    :indeterminate="indeterminate"
    @change="handleCheckAllChange"
  >
    全选
  </HrtCheckbox>
  <div class="hrt-my-1 hrt-border-t hrt-border-neutral-600 hrt-border-solid" />
  <HrtCheckboxGroup v-model="modelValue">
    <HrtCheckbox label="选项1" value="value1">
      选项1
    </HrtCheckbox>
    <HrtCheckbox label="选项2" value="value2" />
    <HrtCheckbox label="选项3" value="value3" />
    <HrtCheckbox label="选项4" value="value4" />
    <HrtCheckbox label="选项5" value="value5" disabled />
  </HrtCheckboxGroup>
</template>
