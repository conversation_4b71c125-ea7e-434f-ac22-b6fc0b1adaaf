<script setup lang="ts">
import type { HrtDialogProps } from '@hrt/components'
import { HrtButton, HrtDialog } from '@hrt/components'
import { shallowRef } from 'vue'

const show = shallowRef(false)
const size = shallowRef<HrtDialogProps['size']>('lg')

function showDialog(dialogSize: HrtDialogProps['size']) {
  show.value = true
  size.value = dialogSize
}
</script>

<template>
  <div class="hrt-flex hrt-gap-2">
    <HrtButton @click="showDialog('xs')">
      展示小弹窗
    </HrtButton>
    <HrtButton @click="showDialog('md')">
      展示中弹窗
    </HrtButton>
    <HrtButton @click="showDialog('lg')">
      展示大弹窗
    </HrtButton>
    <HrtButton @click="showDialog('xl')">
      展示超大弹窗
    </HrtButton>
  </div>
  <HrtDialog v-model="show" title="标题" :size="size">
    <p>这是一段内容</p>
    <p>这是一段内容</p>
    <p>这是一段内容</p>
    <p>这是一段内容</p>
    <p>这是一段内容</p>
    <p>这是一段内容</p>
    <p>这是一段内容</p>
    <p>这是一段内容</p>
    <p>这是一段内容</p>
    <p>这是一段内容</p>
    <p>这是一段内容</p>
    <p>这是一段内容</p>
    <p>这是一段内容</p>
    <p>这是一段内容</p>
    <p>这是一段内容</p>
  </HrtDialog>
</template>
