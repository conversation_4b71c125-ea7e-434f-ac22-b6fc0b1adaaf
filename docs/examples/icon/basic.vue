<script setup lang="ts">
import { HrtBriefcaseLine, HrtCustomer } from '@hrt/icons'

const icons = [
  {
    name: 'HrtBriefcaseLine',
    component: HrtBriefcaseLine,
  },
  {
    name: 'HrtCustomer',
    component: HrtCustomer,
  },
  {
    name: 'HrtBriefcaseLine',
    component: HrtBriefcaseLine,
  },
  {
    name: 'HrtCustomer',
    component: HrtCustomer,
  },
  {
    name: 'HrtBriefcaseLine',
    component: HrtBriefcaseLine,
  },
  {
    name: 'HrtCustomer',
    component: HrtCustomer,
  },
]
</script>

<template>
  <div class="hrt-grid hrt-grid-cols-4">
    <div
      v-for="(item, index) in icons"
      :key="item.name"
      class="hrt-p-4 hrt-border hrt-flex hrt-items-center hrt-justify-center hrt-flex-col hrt-cursor-pointer hrt-border-neutral-500"
      :class="{
        'hrt-border-l-0': index % 4 !== 0,
        'hrt-border-t-0': index >= 4,
      }"
    >
      <component :is="item.component" />
      <p class="hrt-font-base hrt-m-0">
        {{ item.name }}
      </p>
    </div>
  </div>
</template>
