<script setup lang="ts">
import { HrtButton, HrtImgPreview } from '@hrt/components'
import { ref } from 'vue'

const visible = ref<boolean>(false)
const imgDatas = ref<string[]>(
  [
    'http://gips0.baidu.com/it/u=3602773692,1512483864&fm=3028&app=3028&f=JPEG&fmt=auto?w=960&h=1280',
    'http://gips2.baidu.com/it/u=207216414,2485641185&fm=3028&app=3028&f=JPEG&fmt=auto?w=1280&h=720',
    'http://gips1.baidu.com/it/u=3874647369,3220417986&fm=3028&app=3028&f=JPEG&fmt=auto?w=720&h=1280'
  ]
)
</script>

<template>
  <HrtButton @click="visible = true">
    图片预览
  </HrtButton>
  <HrtImgPreview v-model="visible" :img-datas="imgDatas" :default-active-index="1" />
</template>
