<script lang="ts" setup>
import { HrtInput } from '@hrt/components'
import { ref } from 'vue'

const input = ref('')
const value = ref('')

function handleChange(val: string | number) {
  value.value = val
}
</script>

<template>
  <HrtInput v-model="input" style="width: 240px" @input="handleChange" />
  {{ value }}
</template>

<style lang="css">
.el-input__inner::placeholder {
  @apply hrt-text-neutral-400;
}
</style>
