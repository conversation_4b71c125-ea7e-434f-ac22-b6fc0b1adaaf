<script lang="ts" setup>
import { Calendar, Search } from '@element-plus/icons-vue'
import { HrtInput } from '@hrt/components'

import { ref } from 'vue'

const input = ref('')
</script>

<template>
  <div class="hrt-grid hrt-grid-cols-2 hrt-gap-2">
    <div class="hrt-flex hrt-flex-col hrt-gap-2">
      <div class="hrt-text-neutral-100 hrt-text-lg hrt-font-medium">
        前置图标
      </div>
      <HrtInput v-model="input" :prefix-icon="Search" />
      <HrtInput v-model="input" :prefix-icon="Calendar" placeholder="请输入" />
      <HrtInput v-model="input" :prefix-icon="Calendar" disabled placeholder="请输入" />
      <HrtInput
        v-model="input"
        style="width: 240px"
        placeholder="Type something"
      >
        <template #prefix>
          <el-icon>
            <search />
          </el-icon>
        </template>
      </HrtInput>
    </div>
    <div class="hrt-flex hrt-flex-col hrt-gap-2">
      <div class="hrt-text-neutral-100 hrt-text-lg hrt-font-medium">
        后置图标
      </div>
      <HrtInput v-model="input" :suffix-icon="Search" />
      <HrtInput v-model="input" :suffix-icon="Calendar" placeholder="请输入" />
      <HrtInput v-model="input" :suffix-icon="Calendar" disabled placeholder="请输入" />
      <HrtInput
        v-model="input"
        placeholder="Type something"
      >
        <template #suffix>
          <el-icon>
            <search />
          </el-icon>
        </template>
      </HrtInput>
    </div>
    <div class="hrt-flex hrt-flex-col hrt-gap-2">
      <div class="hrt-text-neutral-100 hrt-text-lg hrt-font-medium">
        后置图标2
      </div>
      <HrtInput v-model="input" :suffix-icon="Search" :show-suffix-bg="true" />
      <HrtInput v-model="input" :suffix-icon="Calendar" placeholder="请输入" :show-suffix-bg="true" />
      <HrtInput v-model="input" :suffix-icon="Calendar" disabled placeholder="请输入" :show-suffix-bg="true" />
      <HrtInput
        v-model="input"
        placeholder="Type something"
        :show-suffix-bg="true"
      >
        <template #suffix>
          <el-icon>
            <search />
          </el-icon>
        </template>
      </HrtInput>
    </div>
  </div>
</template>

<style lang="css">
.el-input__inner::placeholder {
  @apply hrt-text-neutral-400;
}

.el-input-group__append,
.el-input-group__prepend {
  padding: 0 12px;
}
</style>
