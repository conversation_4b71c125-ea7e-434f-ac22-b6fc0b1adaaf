<script lang="ts" setup>
import { Search } from '@element-plus/icons-vue'
import { HrtInput } from '@hrt/components'

import { ref } from 'vue'

const input = ref('')
</script>

<template>
  <div class="hrt-grid hrt-grid-cols-2 hrt-gap-2">
    <div class="hrt-flex hrt-flex-col hrt-gap-2">
      <div class="hrt-text-neutral-100 hrt-text-lg hrt-font-medium">
        后置图标
      </div>
      <HrtInput v-model="input" :suffix-icon="Search" />
      <HrtInput
        v-model="input"
        placeholder="Type something"
        :style="{
          '--el-fill-color-light': '#2E6BE6',
        }"
        :show-suffix-bg="true"
      >
        <template #suffix>
          <div class="hrt-text-white">
            搜索
          </div>
        </template>
      </HrtInput>

      <HrtInput
        v-model="input"
        placeholder="Type something"
        :style="{
          '--el-fill-color-light': '#2E6BE6',
        }"
        :show-suffix-bg="true"
      >
        <template #suffix>
          <div>
            <el-icon style="color: white;">
              <search />
            </el-icon>
          </div>
        </template>
      </HrtInput>
    </div>
  </div>
</template>

<style lang="css">
.el-input__inner::placeholder {
  @apply hrt-text-neutral-400;
}

.el-input-group__append,
.el-input-group__prepend {
  padding: 0 12px;
}
</style>
