<script lang="ts" setup>
import { HrtInput } from '@hrt/components'
import { ref } from 'vue'

const input = ref('')
</script>

<template>
  <div class="hrt-flex hrt-flex-col hrt-gap-2">
    <HrtInput v-model="input" size="mini" placeholder="80px" />
    <HrtInput v-model="input" size="small" placeholder="120px" />
    <HrtInput v-model="input" size="middle" placeholder="160px" />
    <HrtInput v-model="input" size="large" placeholder="240px" />
    <HrtInput v-model="input" size="super" placeholder="400px" />
  </div>
</template>

<style lang="css">
.el-input__inner::placeholder {
  @apply hrt-text-neutral-400;
}
</style>
