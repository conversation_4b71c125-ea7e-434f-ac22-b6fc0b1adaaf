<script setup lang="ts">
import { HrtCol, HrtRow } from '@hrt/components'
</script>

<template>
  <div class="hrt-grid hrt-grid-cols-1 hrt-gap-4">
    <HrtRow>
      <HrtCol :span="3">
        <div class="hrt-rounded-sm hrt-min-h-9 hrt-px-2 hrt-leading-9 hrt-bg-neutral-500">
          col-3
        </div>
      </HrtCol>
      <HrtCol :span="3" :offset="3">
        <div class="hrt-rounded-sm hrt-min-h-9 hrt-px-2 hrt-leading-9 hrt-bg-neutral-600">
          col-3
        </div>
      </HrtCol>
    </HrtRow>
    <HrtRow>
      <HrtCol :span="3" :offset="3">
        <div class="hrt-rounded-sm hrt-min-h-9 hrt-px-2 hrt-leading-9 hrt-bg-neutral-600">
          col-3
        </div>
      </HrtCol>
      <HrtCol :span="3" :offset="3">
        <div class="hrt-rounded-sm hrt-min-h-9 hrt-px-2 hrt-leading-9 hrt-bg-neutral-500">
          col-3
        </div>
      </HrtCol>
    </HrtRow>
    <HrtRow>
      <HrtCol :span="6" :offset="3">
        <div class="hrt-rounded-sm hrt-min-h-9 hrt-px-2 hrt-leading-9 hrt-bg-neutral-600">
          col-6
        </div>
      </HrtCol>
    </HrtRow>
  </div>
</template>
