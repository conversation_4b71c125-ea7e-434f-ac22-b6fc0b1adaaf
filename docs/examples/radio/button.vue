<script setup lang="ts">
import { HrtRadioButton, HrtRadioGroup } from '@hrt/components'
import { ref } from 'vue'

const value = ref('1')
const value2 = ref('1')
</script>

<template>
  <div class="hrt-grid hrt-grid-cols-1 hrt-gap-4">
    <div>
      <HrtRadioGroup v-model="value">
        <HrtRadioButton value="1">
          内容一
        </HrtRadioButton>
        <HrtRadioButton value="2">
          内容二
        </HrtRadioButton>
        <HrtRadioButton value="3">
          内容三
        </HrtRadioButton>
        <HrtRadioButton value="4">
          内容四
        </HrtRadioButton>
      </HrtRadioGroup>
      {{ value }}
    </div>
    <HrtRadioGroup v-model="value" :disabled="true">
      <HrtRadioButton value="1">
        内容一
      </HrtRadioButton>
      <HrtRadioButton value="2">
        内容二
      </HrtRadioButton>
      <HrtRadioButton value="3">
        内容三
      </HrtRadioButton>
      <HrtRadioButton value="4">
        内容四
      </HrtRadioButton>
    </HrtRadioGroup>
    <div>
      <HrtRadioGroup v-model="value2">
        <HrtRadioButton value="1" selected-type="outline">
          内容一
        </HrtRadioButton>
        <HrtRadioButton value="2" selected-type="outline">
          内容二
        </HrtRadioButton>
        <HrtRadioButton value="3" selected-type="outline">
          内容三
        </HrtRadioButton>
        <HrtRadioButton value="4" selected-type="outline">
          内容四
        </HrtRadioButton>
      </HrtRadioGroup>
      {{ value2 }}
    </div>
  </div>
</template>
