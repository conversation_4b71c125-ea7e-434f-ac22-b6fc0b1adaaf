<script setup lang="ts">
import { HrtRadio, HrtRadioGroup } from '@hrt/components'
import { ref } from 'vue'

const value = ref('1')
</script>

<template>
  <div>
    <h3 class="text-lg font-bold">
      水平排列
    </h3>
    <HrtRadioGroup v-model="value">
      <HrtRadio value="1">
        内容一
      </HrtRadio>
      <HrtRadio value="2">
        内容二
      </HrtRadio>
      <HrtRadio value="3">
        内容三
      </HrtRadio>
      <HrtRadio value="4">
        内容四
      </HrtRadio>
    </HrtRadioGroup>
  </div>
  <div>
    <h3 class="text-lg font-bold">
      垂直排列
    </h3>
    <HrtRadioGroup v-model="value" direction="vertical">
      <HrtRadio value="1">
        内容一
      </HrtRadio>
      <HrtRadio value="2">
        内容二
      </HrtRadio>
      <HrtRadio value="3">
        内容三
      </HrtRadio>
      <HrtRadio value="4">
        内容四
      </HrtRadio>
    </HrtRadioGroup>
  </div>
</template>
