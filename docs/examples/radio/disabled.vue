<script setup lang="ts">
import { HrtRadio, HrtRadioGroup } from '@hrt/components'
import { ref } from 'vue'

const value = ref('1')
</script>

<template>
  <HrtRadioGroup v-model="value">
    <HrtRadio value="1" disabled>
      内容一
    </HrtRadio>
    <HrtRadio value="2" disabled>
      内容二
    </HrtRadio>
    <HrtRadio value="3">
      内容三
    </HrtRadio>
    <HrtRadio value="4">
      内容四
    </HrtRadio>
  </HrtRadioGroup>
</template>
