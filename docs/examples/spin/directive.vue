<script setup lang="ts">
import { HrtButton } from '@hrt/components'
import { ref } from 'vue'

const loading = ref(false)
</script>

<template>
  <HrtButton @click="loading = !loading">
    切换加载状态
  </HrtButton>
  <div
    v-spin="{
      text: '正在加速...',
      direction: 'horizontal',
      show: loading,
    }"
    class="hrt-w-full hrt-h-[150px] hrt-overflow-auto hrt-mt-4"
  >
    <div class="hrt-p-4">
      <h3 class="hrt-text-lg hrt-font-semibold hrt-mb-3">
        数据加载中...
      </h3>
      <p class="hrt-text-gray-600 hrt-leading-relaxed hrt-mb-4">
        当前页面正在加载重要的业务数据，请您耐心等待。我们的系统正在从服务器获取最新的信息，
        包括用户配置、业务统计数据以及相关的操作记录。这个过程通常只需要几秒钟的时间，
        但在网络状况不佳或数据量较大的情况下，可能会稍微延长一些。
      </p>
      <p class="hrt-text-gray-600 hrt-leading-relaxed hrt-mb-4">
        在等待期间，您可以准备接下来要进行的操作。加载完成后，您将看到完整的数据展示界面，
        包括详细的图表分析、数据表格以及相关的操作按钮。我们致力于为您提供最佳的用户体验，
        感谢您的理解与支持。
      </p>
      <div class="hrt-text-sm hrt-text-gray-500">
        提示：如果加载时间过长，请检查网络连接或刷新页面重试。
      </div>
    </div>
  </div>
</template>
