<script setup lang="ts">
import { HrtSwitch } from '@hrt/components'
import { ref } from 'vue'

const modelValue = ref(false)
const modelValue2 = ref(true)
</script>

<template>
  <HrtSwitch v-model="modelValue" size="large" active-text="开" inactive-text="关" />
  <br>
  <HrtSwitch v-model="modelValue" size="default" active-text="开" inactive-text="关" />
  <br>
  <HrtSwitch
    v-model="modelValue2"
    size="small"
    active-text="开"
    inactive-text="关"
  />
</template>
