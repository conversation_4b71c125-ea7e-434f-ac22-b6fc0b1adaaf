<script setup lang="ts">
import { HrtSwitch } from '@hrt/components'
import { ref } from 'vue'

const modelValue = ref(false)
const modelValue2 = ref(true)
</script>

<template>
  <div class="hrt-flex hrt-items-center hrt-gap-2">
    <span>关闭</span>
    <HrtSwitch v-model="modelValue" />
  </div>
  <br>
  <div class="hrt-flex hrt-items-center hrt-gap-2">
    <span>打开</span>
    <HrtSwitch v-model="modelValue2" />
  </div>
  <br>
  <div class="hrt-flex hrt-items-center hrt-gap-2">
    <span>关闭不可打开</span>
    <HrtSwitch v-model="modelValue" disabled />
  </div>
  <br>
  <div class="hrt-flex hrt-items-center hrt-gap-2">
    <span>打开不可关闭</span>
    <HrtSwitch v-model="modelValue2" disabled />
  </div>
</template>
