<script setup lang="ts">
import { HrtTable, HrtTableColumn } from '@hrt/components'
import { ref } from 'vue'

interface TableRow {
  id: number
  name: string
  age: number
  address: string
  email: string
  phone: string
  company: string
  department: string
  position: string
  status: string
  createdAt: string
  updatedAt: string
  role: string
  gender: string
}

const tableData = ref<TableRow[]>([])
const firstNames = ['张', '李', '王', '赵', '刘', '陈', '杨', '黄', '周', '吴']
const lastNames = ['三', '四', '五', '六', '七', '八', '九', '十', '伟', '芳', '敏', '静', '强']
const addresses = ['北京市海淀区', '上海市浦东新区', '深圳市南山区', '广州市天河区', '杭州市西湖区']
const companies = ['科技有限公司A', '互联网公司B', '金融集团C', '电子商务公司D']
const departments = ['研发部', '产品部', '市场部', '销售部', '人力资源部']
const positions = ['工程师', '产品经理', '设计师', '销售代表', 'HR']
const statuses = ['在职', '离职', '试用期']
const roles = ['管理员', '普通用户', '访客']
const genders = ['男', '女']

for (let i = 1; i <= 20; i++) {
  tableData.value.push({
    id: i,
    name: `${firstNames[Math.floor(Math.random() * firstNames.length)]}${lastNames[Math.floor(Math.random() * lastNames.length)]}`,
    age: Math.floor(Math.random() * 40) + 18,
    address: addresses[Math.floor(Math.random() * addresses.length)],
    email: `user${i}@example.com`,
    phone: `138${String(Math.random()).slice(2, 10)}`,
    company: companies[Math.floor(Math.random() * companies.length)],
    department: departments[Math.floor(Math.random() * departments.length)],
    position: positions[Math.floor(Math.random() * positions.length)],
    status: statuses[Math.floor(Math.random() * statuses.length)],
    createdAt: new Date(Date.now() - Math.random() * 1000 * 3600 * 24 * 30).toISOString().split('T')[0],
    updatedAt: new Date().toISOString().split('T')[0],
    role: roles[Math.floor(Math.random() * roles.length)],
    gender: genders[Math.floor(Math.random() * genders.length)],
  })
}
</script>

<template>
  <HrtTable :data="tableData" height="400">
    <HrtTableColumn prop="id" label="ID" width="80" fixed />
    <HrtTableColumn prop="name" label="姓名" width="120" fixed />
    <HrtTableColumn prop="gender" label="性别" width="80" />
    <HrtTableColumn prop="age" label="年龄" width="80" />
    <HrtTableColumn prop="email" label="邮箱" width="200" />
    <HrtTableColumn prop="phone" label="电话" width="150" />
    <HrtTableColumn prop="address" label="地址" width="250" />
    <HrtTableColumn prop="company" label="公司" width="180" />
    <HrtTableColumn prop="department" label="部门" width="120" />
    <HrtTableColumn prop="position" label="职位" width="120" />
    <HrtTableColumn prop="role" label="角色" width="120" />
    <HrtTableColumn prop="createdAt" label="创建日期" width="150" />
    <HrtTableColumn prop="updatedAt" label="更新日期" width="150" />
    <HrtTableColumn prop="status" label="状态" width="100" fixed="right" />
  </HrtTable>
</template>

<style lang="css">
.vp-doc table {
  margin: unset !important;
}
.vp-doc th,
.vp-doc td {
  border: none !important;
}
.vp-doc tr {
  border-top: none !important;
}
</style>
