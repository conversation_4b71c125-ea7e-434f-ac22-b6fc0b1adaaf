<script setup lang="ts">
import { HrtTable, HrtTableColumn } from '@hrt/components'
import { ref } from 'vue'

const tableData = ref([
  {
    name: '张三',
    age: 18,
    address: '北京市海淀区',
  },
  {
    name: '李四',
    age: 20,
    address: '北京市朝阳区',
  },
  {
    name: '王五',
    age: 22,
    address: '北京市昌平区',
  },
  {
    name: '赵六',
    age: 24,
    address: '北京市顺义区',
  },
])
</script>

<template>
  <HrtTable :data="tableData" stripe>
    <HrtTableColumn prop="name" label="姓名" />
    <HrtTableColumn prop="age" label="年龄" />
    <HrtTableColumn prop="address" label="地址" />
  </HrtTable>
</template>

<style lang="css">
.vp-doc table {
  margin: unset !important;
}
.vp-doc th,
.vp-doc td {
  border: none !important;
}
.vp-doc tr {
  border-top: none !important;
}
</style>
