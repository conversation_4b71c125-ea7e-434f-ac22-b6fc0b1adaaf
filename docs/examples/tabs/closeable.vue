<script setup lang="ts">
import { HrtTabPane, HrtTabs } from '@hrt/components'
import { ref } from 'vue'

const active = ref('1')
const tabs = ref(
  [
    {
      label: 'Tab 1',
      name: '1',
      content: 'Tab 1 Content',
    },
    {
      label: 'Tab 2',
      name: '2',
      content: 'Tab 2 Content',
    },
    {
      label: 'Tab 3',
      name: '3',
      content: 'Tab 3 Content',
      disabled: true,
    },
  ],
)

function handleClose(name: string) {
  tabs.value = tabs.value.filter(tab => tab.name !== name)
}
</script>

<template>
  <HrtTabs
    v-model="active"
    :closable="true"
    type="card"
    @close="handleClose"
  >
    <HrtTabPane
      v-for="tab in tabs"
      :key="tab.name"
      :label="tab.label"
      :name="tab.name"
      :disabled="tab.disabled"
    >
      {{ tab.content }}
    </HrtTabPane>
  </HrtTabs>
</template>

<style lang="css">
.vp-doc li + li {
  margin-top: 0 !important;
}
.vp-doc ul {
  list-style: none !important;
}
.vp-doc ul,
.vp-doc ol {
  padding-left: 0 !important;
  margin: 0 !important;
}
</style>
