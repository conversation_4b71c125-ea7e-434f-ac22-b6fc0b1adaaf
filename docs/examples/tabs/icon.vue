<script setup lang="tsx">
import { Close, House, Setting } from '@element-plus/icons-vue'
import { HrtTabPane, HrtTabs } from '@hrt/components'
import { ref } from 'vue'

const active = ref('1')
</script>

<template>
  <HrtTabs v-model="active" type="card">
    <HrtTabPane label="Tab 1" name="1" :icon="Setting">
      Tab 1 Content
    </HrtTabPane>
    <HrtTabPane label="Tab 2" name="2">
      Tab 2 Content
    </HrtTabPane>
    <HrtTabPane label="Tab 3" name="3" :icon="House">
      Tab 3 Content
    </HrtTabPane>
    <HrtTabPane label="Tab 4" name="4" :disabled="true" :icon="Close">
      Tab 4 Content
    </HrtTabPane>
    <HrtTabPane label="Tab 5" name="5">
      Tab 5 Content
    </HrtTabPane>
  </HrtTabs>
</template>

<style lang="css" scoped>
.is-card .hrt-tabs-nav-item:first-child {
  /* 示例样式 */
  background-color: #f0f0f0;
  border-bottom-color: transparent;
  border-radius: 4px 4px 0 0;
}

/* 原有样式保持不变 */
.vp-doc li + li {
  margin-top: 0 !important;
}
.vp-doc ul {
  list-style: none !important;
}
.vp-doc ul,
.vp-doc ol {
  padding-left: 0 !important;
  margin: 0 !important;
}
</style>
