<script lang="ts" setup>
import { HrtCheckTag } from '@hrt/components'
import { ref } from 'vue'

const checked1 = ref(true)
const checked2 = ref(true)
const checked3 = ref(true)
const checked4 = ref(true)
const checked5 = ref(true)

function onChange1(status: boolean) {
  checked1.value = status
}

function onChange2(status: boolean) {
  checked2.value = status
}

function onChange3(status: boolean) {
  checked3.value = status
}

function onChange4(status: boolean) {
  checked4.value = status
}

function onChange5(status: boolean) {
  checked5.value = status
}
</script>

<template>
  <div class="hrt-flex hrt-gap-2 hrt-mt-4">
    <span>常规：</span>
    <HrtCheckTag :checked="checked1" type="primary" @change="onChange1">
      Tag 1
    </HrtCheckTag>
    <HrtCheckTag :checked="checked2" type="success" @change="onChange2">
      Tag 2
    </HrtCheckTag>
    <HrtCheckTag :checked="checked3" type="info" @change="onChange3">
      Tag 3
    </HrtCheckTag>
    <HrtCheckTag :checked="checked4" type="warning" @change="onChange4">
      Tag 4
    </HrtCheckTag>
    <HrtCheckTag :checked="checked5" type="danger" @change="onChange5">
      Tag 5
    </HrtCheckTag>
  </div>
  <div class="hrt-flex hrt-gap-2 hrt-mt-4">
    <span>禁用：</span>
    <HrtCheckTag disabled type="primary">
      Tag 6
    </HrtCheckTag>
    <HrtCheckTag disabled type="success">
      Tag 7
    </HrtCheckTag>
    <HrtCheckTag disabled type="info">
      Tag 8
    </HrtCheckTag>
    <HrtCheckTag disabled type="warning">
      Tag 9
    </HrtCheckTag>
    <HrtCheckTag disabled type="danger">
      Tag 10
    </HrtCheckTag>
  </div>
</template>
