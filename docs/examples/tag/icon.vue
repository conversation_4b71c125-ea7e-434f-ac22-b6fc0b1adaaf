<script lang="ts" setup>
import { Calendar, Search } from '@element-plus/icons-vue'
import { HrtTag } from '@hrt/components'
</script>

<template>
  <p>前置图标</p>
  <div class="hrt-flex hrt-gap-2">
    <HrtTag :prefix-icon="Calendar" type="primary">
      Tag 1
    </HrtTag>
    <HrtTag :prefix-icon="Search" type="success">
      Tag 2
    </HrtTag>
    <HrtTag :prefix-icon="Calendar" type="info">
      Tag 3
    </HrtTag>
    <HrtTag :prefix-icon="Search" type="warning">
      Tag 4
    </HrtTag>
    <HrtTag :prefix-icon="Calendar" type="danger">
      Tag 5
    </HrtTag>
  </div>

  <p>后置图标</p>
  <div class="hrt-flex hrt-gap-2">
    <HrtTag :suffix-icon="Calendar" type="primary">
      Tag 1
    </HrtTag>
    <HrtTag :suffix-icon="Search" type="success">
      Tag 2
    </HrtTag>
    <HrtTag :suffix-icon="Calendar" type="info">
      Tag 3
    </HrtTag>
    <HrtTag :suffix-icon="Search" type="warning">
      Tag 4
    </HrtTag>
    <HrtTag :suffix-icon="Calendar" type="danger">
      Tag 5
    </HrtTag>
  </div>
</template>

<style lang="css" scoped></style>
