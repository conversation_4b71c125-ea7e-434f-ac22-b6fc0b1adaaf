<script lang="ts" setup>
import type { TagProps } from 'element-plus'
import { HrtTag } from '@hrt/components'
import { ref } from 'vue'

interface TagsItem {
  name: string
  type: TagProps['type']
}

const tags = ref<TagsItem[]>([
  { name: 'Tag 1', type: 'primary' },
  { name: 'Tag 2', type: 'success' },
  { name: 'Tag 3', type: 'info' },
  { name: 'Tag 4', type: 'warning' },
  { name: 'Tag 5', type: 'danger' },
])
</script>

<template>
  <div class="hrt-flex hrt-gap-2">
    <HrtTag v-for="tag in tags" :key="tag.name" closable :type="tag.type">
      {{ tag.name }}
    </HrtTag>
  </div>
</template>
