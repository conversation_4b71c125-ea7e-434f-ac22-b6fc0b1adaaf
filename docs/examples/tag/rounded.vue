<script lang="ts" setup>
import type { TagProps } from 'element-plus'
import { HrtTag } from '@hrt/components'
import { ref } from 'vue'

interface Item { type: TagProps['type'], label: string }

const items = ref<Array<Item>>([
  { type: 'primary', label: 'Tag 1' },
  { type: 'success', label: 'Tag 2' },
  { type: 'info', label: 'Tag 3' },
  { type: 'warning', label: 'Tag 4' },
  { type: 'danger', label: 'Tag 5' },
])
</script>

<template>
  <div class="hrt-flex hrt-gap-2">
    <HrtTag
      v-for="item in items"
      :key="item.label"
      :type="item.type"
      effect="dark"
    >
      {{ item.label }}
    </HrtTag>
  </div>
  <div class="hrt-flex hrt-gap-2 hrt-mt-4">
    <HrtTag
      v-for="item in items"
      :key="item.label"
      :type="item.type"
      effect="light"
      round
    >
      {{ item.label }}
    </HrtTag>
  </div>
  <div class="hrt-flex hrt-gap-2 hrt-mt-4">
    <HrtTag
      v-for="item in items"
      :key="item.label"
      :type="item.type"
      effect="plain"
      round
    >
      {{ item.label }}
    </HrtTag>
  </div>
</template>
