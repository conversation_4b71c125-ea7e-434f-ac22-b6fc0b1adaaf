<script setup lang="ts">
import { HrtText, HrtTitle } from '@hrt/components'
</script>

<template>
  <div class="hrt-flex hrt-flex-col hrt-gap-2 hrt-items-start">
    <HrtText>
      Span
    </HrtText>
    <HrtText tag="p">
      This is a paragraph.
    </HrtText>
    <HrtText tag="b">
      Bold
    </HrtText>
    <HrtText type="info" tag="i">
      Italic
    </HrtText>
    <HrtTitle>
      This is
      <HrtText type="warning" tag="sub">
        subscript
      </HrtText>
    </HrtTitle>
    <HrtTitle>
      This is
      <HrtText type="warning" tag="sup">
        superscript
      </HrtText>
    </HrtTitle>
    <HrtText tag="ins">
      Inserted
    </HrtText>
    <HrtText tag="del">
      Deleted
    </HrtText>
    <HrtText tag="mark">
      Marked
    </HrtText>
  </div>
</template>

<style lang="css" scoped>
.vp-doc {
  h1 {
    @apply hrt-text-xl hrt-font-medium hrt-m-0 hrt-tracking-normal;
  }
  h2 {
    @apply hrt-text-base hrt-font-medium hrt-m-0 hrt-tracking-normal;
  }
  h3 {
    @apply hrt-text-sm hrt-font-medium hrt-m-0 hrt-tracking-normal;
  }
  p {
    @apply hrt-m-0;
  }
}
</style>
