<script setup lang="ts">
import { CircleCloseFilled, Plus } from '@element-plus/icons-vue'
import { HrtUpload, type HrtUploadModelValue } from '@hrt/components'
import { ElIcon } from 'element-plus'
import { ref } from 'vue'

const fileList = ref<HrtUploadModelValue>([])
</script>

<template>
  <div class="hrt-w-[350px]">
    <HrtUpload
      v-model="fileList"
      list-type="picture"
      :auto-upload="false"
      :multiple="false"
      :show-file-list="false"
    >
      <div class="avatar-upload">
        <template v-if="fileList[0]?.url">
          <img :src="fileList[0].url">
          <ElIcon class="avatar-upload-remove" @click.stop="fileList = []">
            <CircleCloseFilled />
          </ElIcon>
        </template>
        <ElIcon v-else>
          <Plus />
        </ElIcon>
      </div>
    </HrtUpload>
  </div>
</template>

<style lang="less">
@import url(./style.less);

.avatar-upload {
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 25px;
  color: var(--el-text-color-secondary);
  border: 1px dashed #cdd0d6;
  background-color: #f7f8fa;
  border-radius: 5px;
  position: relative;
  &:hover {
    border-color: var(--el-color-primary);
    .avatar-upload-remove {
      display: block;
    }
  }
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: inherit;
  }
  .avatar-upload-remove {
    position: absolute;
    right: -8px;
    top: -8px;
    z-index: 1;
    font-size: 16px;
    color: var(--el-color-danger);
    cursor: pointer;
    display: none;
  }
}
</style>
