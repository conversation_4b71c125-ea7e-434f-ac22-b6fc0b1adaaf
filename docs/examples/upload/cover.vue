<script setup lang="ts">
import { HrtUpload, type HrtUploadModelValue } from '@hrt/components'
import { ref } from 'vue'

const fileList = ref<HrtUploadModelValue>([])
</script>

<template>
  <div class="hrt-w-[350px]">
    <HrtUpload
      v-model="fileList"
      list-type="text"
      :auto-upload="false"
      :multiple="false"
    />
  </div>
</template>

<style lang="less">
@import url(./style.less);
</style>
