<script setup lang="ts">
import { HrtUpload, type HrtUploadModelValue } from '@hrt/components'
import { ref } from 'vue'

const fileList1 = ref<HrtUploadModelValue>([])
const fileList2 = ref<HrtUploadModelValue>([])
const fileList3 = ref<HrtUploadModelValue>([])
</script>

<template>
  <div class="hrt-w-[350px]">
    <div class="hrt-mb-[20px]">
      <HrtUpload
        v-model="fileList1"
        list-type="text"
        :limit="5"
        drag
        qiniu-token="Cb_l0Q4F6M-lPzLVglwUgBnqeiOTo136faFI9cbv:NsgK1FU6BJq0tl9J12rU_UEWeDs=:eyJzY29wZSI6ImhydC1kZXZpbWFnZXMiLCJkZWFkbGluZSI6MTc0OTYzODg3MH0="
        qiniu-domain="https://hrt-devimages.hrttest.cn/"
      />
    </div>
    <div class="hrt-mb-[20px]">
      <HrtUpload
        v-model="fileList2"
        list-type="picture"
        :limit="5"
        drag
        qiniu-token="Cb_l0Q4F6M-lPzLVglwUgBnqeiOTo136faFI9cbv:NsgK1FU6BJq0tl9J12rU_UEWeDs=:eyJzY29wZSI6ImhydC1kZXZpbWFnZXMiLCJkZWFkbGluZSI6MTc0OTYzODg3MH0="
        qiniu-domain="https://hrt-devimages.hrttest.cn/"
      />
    </div>
    <div>
      <HrtUpload
        v-model="fileList3"
        list-type="picture-card"
        :limit="5"
        drag
        qiniu-token="Cb_l0Q4F6M-lPzLVglwUgBnqeiOTo136faFI9cbv:NsgK1FU6BJq0tl9J12rU_UEWeDs=:eyJzY29wZSI6ImhydC1kZXZpbWFnZXMiLCJkZWFkbGluZSI6MTc0OTYzODg3MH0="
        qiniu-domain="https://hrt-devimages.hrttest.cn/"
      />
    </div>
  </div>
</template>

<style lang="less">
@import url(./style.less);
</style>
