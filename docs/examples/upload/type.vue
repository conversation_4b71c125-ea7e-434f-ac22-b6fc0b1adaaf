<script setup lang="ts">
import { HrtUpload, type HrtUploadModelValue } from '@hrt/components'
import { ref } from 'vue'

const fileList1 = ref<HrtUploadModelValue>([])
const fileList2 = ref<HrtUploadModelValue>([])
const fileList3 = ref<HrtUploadModelValue>([])
</script>

<template>
  <div class="hrt-w-[350px]">
    <div class="hrt-mb-[20px]">
      <HrtUpload
        v-model="fileList1"
        list-type="text"
        :limit="5"
        :auto-upload="false"
        drag
      />
    </div>
    <div class="hrt-mb-[20px]">
      <HrtUpload
        v-model="fileList2"
        list-type="picture"
        :limit="5"
        :auto-upload="false"
        drag
      />
    </div>
    <div>
      <HrtUpload
        v-model="fileList3"
        list-type="picture-card"
        :limit="5"
        :auto-upload="false"
        drag
      />
    </div>
  </div>
</template>

<style lang="less">
@import url(./style.less);
</style>
