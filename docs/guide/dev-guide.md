# 开发指南

## 启动项目

使用命令

```bash
pnpm i
```

该项目将安装所有依赖

## 网站预览

使用命令

```shell
pnpm docs:dev
```

该项目将启动网站，网站内你可以预览全部现有组件

## 本地开发

本仓库基于monorepo的方式管理多个npm包，包存在于 `packages` 目录下，以
在 `components` 中开发一个 `Avatar` 组件为例进入组件开发:

### 创建组件

在 `packages/components/src` 文件夹下新建一个 `avatar` 文件夹，在文件夹下新建2个文件：

- `Avatar.vue`: 组件逻辑与样式
- `index.ts`: 负责组件的导出（因为要支持树摇，需要给组件添加 `install` 方法）

::: info `index.ts` 文件内容基本都差不多，可以参考下面的代码

```ts
import type { App } from 'vue'
import Avatar from './Avatar.vue'

Avatar.install = (app: App) => {
  app.component(Avatar.name!, Avatar)
}

export const HrtAvatar = Avatar

export default HrtAvatar
```

:::

### 组件预览与调试

组件的预览与调试在网站文档上进行，运行 `pnpm docs:dev` 启动文档，在 `docs/components` 目录下新建 `avatar.md` 文件，在 `docs/.vitepress/config.mts` 中配置刚刚新建的文档的路由。

在 `docs/examples` 目录下新建 `avatar` 文件夹，文件夹下可以创建多个示例vue文件用作展示代码示例，比如我们创建一个 `avatar/basic.vue` 文件，在Markdown文档中通过

```
::: demo
avatar/basic
:::
```

的形式引入，即可在网站文档上看见组件的展示效果并进行调试。

### 开发规范

- 组件的文件名称遵循kebab-case的命名规则：（小写，以-分割）；
- 组件的 `<style>` 标签不能添加 `scope`；
- 组件自定义的CSS类名需要以 `hrt-` 开头；
- 组件为 `tailwindcss` 配置了前缀 `hrt-`，如需使用 `tailwindcss` 提供的类名时也需要加上 `hrt-` 使用（如 `hrt-flex`）；
- 默认组件都存在 `style` 标签并且存在内容，如果组件不存在 `style` 标签的话需要在 `auto-import-resolver.ts` 中配置，避免打包时去引入对应的静态资源。
