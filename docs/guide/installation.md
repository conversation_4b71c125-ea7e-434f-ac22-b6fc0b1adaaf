# 安装

## 兼容性

HeartMed Design 支持现代浏览器。

## 使用包管理器安装

### 安装前配置

因为 HeartMed Design 为内部组件库，所以你需要在项目根目录下创建 `.npmrc` 文件，并添加以下内容：

```bash
# 设置华为源
registry=http://mirrors.huaweicloud.com/repository/npm/
@hrt:registry=http://***************:8081/repository/hrt-npm-hosted/
//***************:8081/repository/hrt-npm-hosted/:_authToken=NpmToken.************************************
```

### 安装

本项目参考 Element-Plus，将组件库分为了两个包：

- `@hrt/components`：提供了基础的组件
- `@hrt/icons`：提供了基础的图标

你可以通过以下方式安装 HeartMed Design：
::: code-group

```bash [pnpm]
$ pnpm add @hrt/components @hrt/icons
```

```bash [npm]
$ npm install @hrt/components @hrt/icons
```

```bash [yarn]
$ yarn add @hrt/components @hrt/icons
```

:::
