# 快速开始

本节将介绍如何在项目中使用 HeartMed Design。

## 用法

### 引入基础样式文件

在项目的 `main.ts` 文件中引入基础样式文件：

```ts
import '@hrt/components/dist/index.css'
```

### 按需引入

**安装 `unplugin-vue-components`**:

```shell
pnpm add -D unplugin-vue-components
```

**配置 vite.config.ts**

```ts
import HrtResolver from '@hrt/components/dist/auto-import-resolver'
import Components from 'unplugin-vue-components/vite'

export default defineConfig({
  plugins: [
    vue(),
    Components({
      resolvers: [
        HrtResolver()
      ],
      dts: true,
      dirs: ['node_modules']
    })
  ],
})
```
