export interface HrtComponentsResolverOptions {
}

function kebabCase(key: string) {
  const result = key.replace(/([A-Z])/g, ' $1').trim()
  return result.split(' ').join('-').toLowerCase()
}

/**
 * 获取组件对应的静态资源
 * @param name 文件名称
 * @returns 组件对应的静态资源
 */
function getSideEffects(name: string) {
  if (['row'].includes(name)) {
    return []
  }
  return [
    `@hrt/components/dist/src/${name}/style/index.css`,
  ]
}

export function HrtComponentsResolver() {
  return [
    (name: string) => {
      if (name.startsWith('Hrt')) {
        const componentDir = kebabCase(name.substring(3))
        return {
          name,
          from: '@hrt/components',
          sideEffects: getSideEffects(componentDir),
        }
      }
    },
  ]
}

export default HrtComponentsResolver
