<script setup lang="ts">
import { ElAutocomplete } from 'element-plus'
import { useAttrs } from 'vue'

interface IProps {
  size?: 'mini' | 'small' | 'middle' | 'large' | 'super'
}
defineOptions({
  name: 'HrtAutocomplete',
  inheritAttrs: false,
})
withDefaults(defineProps<IProps>(), {
  size: 'large',
})
const sizeWidth = {
  mini: 80,
  small: 120,
  middle: 160,
  large: 240,
  super: 400,
}
const attrs = useAttrs()
function hilight(text: string) {
  const searchText = attrs.modelValue as string
  const res = text.replace(searchText, '<span style="color:#2E6BE6">$&</span>')
  return res
}
</script>

<template>
  <ElAutocomplete
    v-bind="attrs"
    class="hrt-autocomplete"
    :style="{ width: `${sizeWidth[size]}px` }"
  >
    <template v-for="(_, key) in $slots" #[key]="slotProps" :key="key">
      <slot :name="key" v-bind="slotProps" />
    </template>
    <template v-if="!$slots.default" #default="{ item }">
      <div class="value" v-html="hilight(item.value)" />
    </template>
  </ElAutocomplete>
</template>

<style scoped lang="less">
.hrt-autocomplete {
  :deep(.el-input__wrapper) {
    border-radius: 2px;
    &:hover {
      box-shadow: 0 0 0 1px var(--el-color-primary) inset;
      caret-color: var(--el-color-primary);
    }
  }
}
</style>

<style lang="less">
.el-autocomplete-suggestion li:hover {
  background: #e6eeff;
}
</style>
