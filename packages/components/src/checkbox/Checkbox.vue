<script setup lang="ts">
import type { CheckboxProps } from 'element-plus'
import { ElCheckbox } from 'element-plus'

export interface HrtCheckboxProps extends Partial<Omit<CheckboxProps, 'modelValue'>> {
}

const props = defineProps<HrtCheckboxProps>()
const emit = defineEmits<{
  change: [value: string | number | boolean]
}>()
const modelValue = defineModel<string | number | boolean>()
</script>

<template>
  <ElCheckbox
    v-model="modelValue"
    class="hrt-checkbox"
    v-bind="props"
    @change="emit('change', $event)"
  >
    <template v-if="$slots.default">
      <slot />
    </template>
    <template v-else>
      {{ props.label }}
    </template>
  </ElCheckbox>
</template>

<style lang="css">
.hrt-checkbox {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  .el-checkbox__input.is-checked + .el-checkbox__label {
    color: var(--hrt-color-neutral-200);
  }
  .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
    --el-checkbox-disabled-checked-input-fill: var(--hrt-color-neutral-400);
  }
  .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner:after {
    border-color: white;
  }
}
.hrt-checkbox input[type='checkbox'] {
  margin-right: 6px;
}
.hrt-checkbox-label {
  user-select: none;
}
.hrt-checkbox input[disabled] + .hrt-checkbox-label {
  color: #ccc;
  cursor: not-allowed;
}
</style>
