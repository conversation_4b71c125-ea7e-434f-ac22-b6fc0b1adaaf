import type { App } from 'vue'
import { ElCheckboxGroup } from 'element-plus'
import Checkbox from './Checkbox.vue'
import CheckboxButton from './CheckboxButton.vue'

Checkbox.install = (app: App) => {
  app.component(Checkbox.name!, Checkbox)
}
CheckboxButton.install = (app: App) => {
  app.component(CheckboxButton.name!, CheckboxButton)
}

ElCheckboxGroup.install = (app: App) => {
  app.component('HrtCheckboxGroup', ElCheckboxGroup)
}

export const HrtCheckbox = Checkbox
export const HrtCheckboxGroup = ElCheckboxGroup
export const HrtCheckboxButton = CheckboxButton
