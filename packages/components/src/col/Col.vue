<script setup lang="ts">
import { inject } from 'vue'

export interface ColProps {
  span?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12
  /** 栅格左侧的间隔格数 */
  offset?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12
  /** 栅格向右移动格数 */
  push?: number
  /** 栅格向左移动格数 */
  pull?: number
}

defineOptions({
  name: 'HrtCol',
})

const {
  span = 1,
  offset = 0,
  push = 0,
  pull = 0,
} = defineProps<ColProps>()

const gutter = inject<number>('gutter', 16)
</script>

<template>
  <div
    :class="{
      'hrt-w-full': span === 12,
      'hrt-w-11/12': span === 11,
      'hrt-w-5/6': span === 10,
      'hrt-w-3/4': span === 9,
      'hrt-w-2/3': span === 8,
      'hrt-w-7/12': span === 7,
      'hrt-w-1/2': span === 6,
      'hrt-w-5/12': span === 5,
      'hrt-w-1/3': span === 4,
      'hrt-w-1/4': span === 3,
      'hrt-w-1/6': span === 2,
      'hrt-w-1/12': span === 1,
      'hrt-ml-full': offset === 12,
      'hrt-ml-11/12': offset === 11,
      'hrt-ml-5/6': offset === 10,
      'hrt-ml-3/4': offset === 9,
      'hrt-ml-2/3': offset === 8,
      'hrt-ml-7/12': offset === 7,
      'hrt-ml-1/2': offset === 6,
      'hrt-ml-5/12': offset === 5,
      'hrt-ml-1/3': offset === 4,
      'hrt-ml-1/4': offset === 3,
      'hrt-ml-1/6': offset === 2,
      'hrt-ml-1/12': offset === 1,
      '-hrt-ml-full': pull === 12,
      '-hrt-ml-11/12': pull === 11,
      '-hrt-ml-5/6': pull === 10,
      '-hrt-ml-3/4': pull === 9,
      '-hrt-ml-2/3': pull === 8,
      '-hrt-ml-7/12': pull === 7,
      '-hrt-ml-1/2': pull === 6,
      '-hrt-ml-5/12': pull === 5,
      '-hrt-ml-1/3': pull === 4,
      '-hrt-ml-1/4': pull === 3,
      '-hrt-ml-1/6': pull === 2,
      '-hrt-ml-1/12': pull === 1,
      'hrt-mr-1/12': push === 1,
      'hrt-mr-1/6': push === 2,
      'hrt-mr-1/4': push === 3,
      'hrt-mr-1/3': push === 4,
      'hrt-mr-5/12': push === 5,
      'hrt-mr-1/2': push === 6,
      'hrt-mr-7/12': push === 7,
      'hrt-mr-2/3': push === 8,
      'hrt-mr-3/4': push === 9,
      'hrt-mr-5/6': push === 10,
      'hrt-mr-11/12': push === 11,
      'hrt-mr-full': push === 12,
    }"
    :style="{
      paddingLeft: gutter ? `${gutter / 2}px` : undefined,
      paddingRight: gutter ? `${gutter / 2}px` : undefined,
    }"
  >
    <slot />
  </div>
</template>

<style>
.hrt-ml-1\/12 {
  margin-left: 8.33333%;
}
.hrt-ml-1\/6 {
  margin-left: 16.66667%;
}
.hrt-ml-1\/4 {
  margin-left: 25%;
}
.hrt-ml-1\/3 {
  margin-left: 33.33333%;
}
.hrt-ml-5\/12 {
  margin-left: 41.66667%;
}
.hrt-ml-1\/2 {
  margin-left: 50%;
}
.hrt-ml-7\/12 {
  margin-left: 58.33333%;
}
.hrt-ml-2\/3 {
  margin-left: 66.66667%;
}
.hrt-ml-3\/4 {
  margin-left: 75%;
}
.hrt-ml-5\/6 {
  margin-left: 83.33333%;
}
.hrt-ml-11\/12 {
  margin-left: 91.66667%;
}
.hrt-ml-full {
  margin-left: 100%;
}

.-hrt-ml-1\/12 {
  margin-left: -8.33333%;
}
.-hrt-ml-1\/6 {
  margin-left: -16.66667%;
}
.-hrt-ml-1\/4 {
  margin-left: -25%;
}
.-hrt-ml-1\/3 {
  margin-left: -33.33333%;
}
.-hrt-ml-5\/12 {
  margin-left: -41.66667%;
}
.-hrt-ml-1\/2 {
  margin-left: -50%;
}
.-hrt-ml-7\/12 {
  margin-left: -58.33333%;
}
.-hrt-ml-2\/3 {
  margin-left: -66.66667%;
}
.-hrt-ml-3\/4 {
  margin-left: -75%;
}
.-hrt-ml-5\/6 {
  margin-left: -83.33333%;
}
.-hrt-ml-11\/12 {
  margin-left: -91.66667%;
}
.-hrt-ml-full {
  margin-left: -100%;
}

/* 添加 mr-* 类的定义 */
.hrt-mr-1\/12 {
  margin-right: 8.33333%;
}
.hrt-mr-1\/6 {
  margin-right: 16.66667%;
}
.hrt-mr-1\/4 {
  margin-right: 25%;
}
.hrt-mr-1\/3 {
  margin-right: 33.33333%;
}
.hrt-mr-5\/12 {
  margin-right: 41.66667%;
}
.hrt-mr-1\/2 {
  margin-right: 50%;
}
.hrt-mr-7\/12 {
  margin-right: 58.33333%;
}
.hrt-mr-2\/3 {
  margin-right: 66.66667%;
}
.hrt-mr-3\/4 {
  margin-right: 75%;
}
.hrt-mr-5\/6 {
  margin-right: 83.33333%;
}
.hrt-mr-11\/12 {
  margin-right: 91.66667%;
}
.hrt-mr-full {
  margin-right: 100%;
}
</style>
