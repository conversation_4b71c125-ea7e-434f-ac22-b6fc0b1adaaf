<script setup lang="ts">
import { ElCollapse } from 'element-plus'
import { useAttrs } from 'vue'

interface IProps {
  ghost?: boolean
}
defineOptions({
  name: 'HrtCollapse',
  inheritAttrs: false,
})
defineProps<IProps>()
const attrs = useAttrs()
</script>

<template>
  <ElCollapse
    v-bind="attrs"
    class="collapse-wrapper" :class="[ghost ? 'ghost' : '']"
  >
    <slot />
  </ElCollapse>
</template>

<style scoped lang="less">
.collapse-wrapper {
  :deep(.el-collapse-item__header) {
    background: #f7f8fa;
    color: #15233f;
    padding: 12px 12px 12px 36px;
    border: 1px solid var(--el-collapse-border-color);
    border-top-color: transparent;
    .el-collapse-item__arrow {
      position: absolute;
      left: 36px;
    }
  }
  :deep(.el-collapse-item__content) {
    padding: 12px 12px 12px 36px;
  }
  &.ghost {
    :deep(.el-collapse-item__header) {
      background: #fff;
    }
  }
}
</style>
