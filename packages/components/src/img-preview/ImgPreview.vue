<script setup lang="ts">
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
import { ElButton, ElDialog, ElIcon, ElMessage } from 'element-plus'
import { computed, nextTick, ref, watch } from 'vue'

interface IProps {
  imgDatas: string[]
  defaultActiveIndex?: number
  title?: string
  width?: number
  top?: string
  lockScroll?: boolean
}

defineOptions({ name: 'HrtImgPreview' })

const props = withDefaults(defineProps<IProps>(), {
  defaultActiveIndex: 0,
  title: '图片预览',
  width: 500,
  top: '7vh',
  lockScroll: true,
})
const visible = defineModel({ default: false })
const activeIndex = ref<number>(0)
const scale = ref<number>(1)
const rotate = ref<number>(0)
const fullscreen = ref<boolean>(false)
const disX = ref<number>(0)
const disY = ref<number>(0)
const imgWidth = ref<number>(0)
const imgHeight = ref<number>(0)
const imgContentWidth = ref<number>(0)
const imgContentHeight = ref<number>(0)
const imgRef = ref()
const imgContentRef = ref()

const dialogHeight = computed(() => {
  const topNumber = Number.parseFloat(props.top)

  if (!Number.isNaN(topNumber)) {
    const topUnit = props.top.replace(/^\d+/, '')

    return `calc(100% - ${topNumber * 2}${topUnit})`
  }

  return '100%'
})

watch(() => visible.value, (newVisivle) => {
  if (newVisivle) {
    activeIndex.value = props.defaultActiveIndex || 0
  }
  else {
    activeIndex.value = 0
    reset()
  }
})

function handleLoad() {
  imgWidth.value = imgRef.value.clientWidth
  imgHeight.value = imgRef.value.clientHeight
  imgContentWidth.value = imgContentRef.value.clientWidth
  imgContentHeight.value = imgContentRef.value.clientHeight
}
function handlePrev() {
  if (activeIndex.value > 0) {
    activeIndex.value -= 1
    reset()
  }
  else {
    ElMessage.warning('已经是第一张了')
  }
}
function handleNext() {
  if (activeIndex.value < props.imgDatas.length - 1) {
    activeIndex.value += 1
    reset()
  }
  else {
    ElMessage.warning('已经是最后一张了')
  }
}
function handleRotate(rotateNum: number) {
  rotate.value += rotateNum
  resetExcludeRotate()

  const isOdd = rotate.value / 90 % 2 !== 0

  // 弹窗横向布局时
  if (imgContentHeight.value >= imgContentWidth.value) {
    // 角度为奇数时，交换宽高
    if (isOdd) {
      // 图片高度超出容器宽度时，设置高度为容器宽度
      if (imgRef.value.clientHeight > imgContentWidth.value) {
        imgRef.value.style.height = `${imgContentWidth.value}px`
      }
      imgWidth.value = imgRef.value.clientHeight
      imgHeight.value = imgRef.value.clientWidth
    }
    else {
      imgRef.value.style.height = 'auto'
      imgWidth.value = imgRef.value.clientWidth
      imgHeight.value = imgRef.value.clientHeight
    }
  }
  else {
    // 角度为奇数时，交换宽高
    if (isOdd) {
      // 图片宽度超出容器高度时，设置宽度为容器高度
      if (imgRef.value.clientWidth > imgContentHeight.value) {
        imgRef.value.style.width = `${imgContentHeight.value}px`
      }
      imgWidth.value = imgRef.value.clientHeight
      imgHeight.value = imgRef.value.clientWidth
    }
    else {
      imgRef.value.style.width = 'auto'
      imgWidth.value = imgRef.value.clientWidth
      imgHeight.value = imgRef.value.clientHeight
    }
  }
}
function handleZoomOut() {
  if (scale.value > 1) {
    scale.value -= 0.1
    updatePosition(imgRef.value.offsetLeft, imgRef.value.offsetTop)
  }
}
function handleZoomIn() {
  if (scale.value < 3) {
    scale.value += 0.1
    updatePosition(imgRef.value.offsetLeft, imgRef.value.offsetTop)
  }
}
function handleWheel(event: WheelEvent) {
  const deta = event.deltaY

  // 鼠标滚轮向上滚动
  if (deta < 0) {
    handleZoomIn()
  }
  // 鼠标滚轮向下滚动
  if (deta > 0) {
    handleZoomOut()
  }
}
async function handleFullscreenChange() {
  fullscreen.value = !fullscreen.value
  reset()
  await nextTick()
  handleLoad()
}
function handleMousedown(event: MouseEvent) {
  event.preventDefault()

  const target = event.target as HTMLElement

  disX.value = event.clientX - target.offsetLeft
  disY.value = event.clientY - target.offsetTop
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}
function handleMouseMove(event: MouseEvent) {
  const left = event.clientX - disX.value
  const top = event.clientY - disY.value

  updatePosition(left, top)
}
function handleMouseUp() {
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
}
function updatePosition(left: number, top: number) {
  const imgViewWidth = imgWidth.value * scale.value
  const imgViewHeight = imgHeight.value * scale.value
  const halfImgShowWidth = imgViewWidth / 2
  const halfImgShowHeight = imgViewHeight / 2
  const diffWidth = imgContentWidth.value - halfImgShowWidth
  const diffHeight = imgContentHeight.value - halfImgShowHeight
  let newLeft = left
  let newTop = top

  if (imgViewWidth > imgContentWidth.value) {
    if (newLeft > halfImgShowWidth) {
      newLeft = halfImgShowWidth
    }
    else if (newLeft < diffWidth) {
      newLeft = diffWidth
    }
    imgRef.value.style.left = `${newLeft}px`
  }
  else {
    imgRef.value.style.left = '50%'
  }
  if (imgViewHeight > imgContentHeight.value) {
    if (newTop > halfImgShowHeight) {
      newTop = halfImgShowHeight
    }
    else if (newTop < diffHeight) {
      newTop = diffHeight
    }
    imgRef.value.style.top = `${newTop}px`
  }
  else {
    imgRef.value.style.top = '50%'
  }
}
function resetExcludeRotate() {
  scale.value = 1
  imgRef.value.style.left = '50%'
  imgRef.value.style.top = '50%'
}
function reset() {
  rotate.value = 0
  resetExcludeRotate()
}
</script>

<template>
  <ElDialog
    v-model="visible"
    v-bind="$attrs"
    modal-class="hrt-img-preview"
    :title="props.title"
    :width="props.width"
    :top="props.top"
    :fullscreen="fullscreen"
    :lock-scroll="props.lockScroll"
  >
    <div class="hrt-img-preview-btns">
      <div class="hrt-flex hrt-items-center">
        <ElButton v-if="fullscreen" link @click="handleFullscreenChange">
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-minimize-icon lucide-minimize">
            <path d="M8 3v3a2 2 0 0 1-2 2H3" />
            <path d="M21 8h-3a2 2 0 0 1-2-2V3" />
            <path d="M3 16h3a2 2 0 0 1 2 2v3" />
            <path d="M16 21v-3a2 2 0 0 1 2-2h3" />
          </svg>
        </ElButton>
        <ElButton v-else link @click="handleFullscreenChange">
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-maximize-icon lucide-maximize">
            <path d="M8 3H5a2 2 0 0 0-2 2v3" />
            <path d="M21 8V5a2 2 0 0 0-2-2h-3" />
            <path d="M3 16v3a2 2 0 0 0 2 2h3" />
            <path d="M16 21h3a2 2 0 0 0 2-2v-3" />
          </svg>
        </ElButton>
        <ElButton link @click="handleZoomOut">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-zoom-out-icon lucide-zoom-out">
            <circle cx="11" cy="11" r="8" /><line x1="21" x2="16.65" y1="21" y2="16.65" />
            <line x1="8" x2="14" y1="11" y2="11" />
          </svg>
        </ElButton>
        <ElButton link @click="handleZoomIn">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-zoom-in-icon lucide-zoom-in">
            <circle cx="11" cy="11" r="8" /><line x1="21" x2="16.65" y1="21" y2="16.65" />
            <line x1="11" x2="11" y1="8" y2="14" /><line x1="8" x2="14" y1="11" y2="11" />
          </svg>
        </ElButton>
      </div>
      <div class="hrt-img-preview-btns-divider" />
      <div class="hrt-flex hrt-items-center">
        <ElButton link @click="handleRotate(-90)">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-rotate-ccw-square-icon lucide-rotate-ccw-square">
            <path d="M20 9V7a2 2 0 0 0-2-2h-6" /><path d="m15 2-3 3 3 3" />
            <path d="M20 13v5a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V7a2 2 0 0 1 2-2h2" />
          </svg>
        </ElButton>
        <ElButton link @click="handleRotate(90)">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-rotate-cw-square-icon lucide-rotate-cw-square">
            <path d="M12 5H6a2 2 0 0 0-2 2v3" /><path d="m9 8 3-3-3-3" />
            <path d="M4 14v4a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2h-2" />
          </svg>
        </ElButton>
      </div>
    </div>
    <div ref="imgContentRef" class="hrt-img-preview-content">
      <ElButton class="hrt-img-preview-prev" link @click="handlePrev">
        <ElIcon><ArrowLeft /></ElIcon>
      </ElButton>
      <img
        ref="imgRef"
        class="hrt-img-preview-img"
        :src="props.imgDatas[activeIndex]"
        :style="{
          transform: `translate(-50%,-50%) scale(${scale}) rotateZ(${rotate}deg)`,
          cursor: scale > 1 ? 'move' : 'pointer',
        }"
        @load="handleLoad"
        @wheel="handleWheel"
        @mousedown="handleMousedown"
      >
      <ElButton class="hrt-img-preview-next" link @click="handleNext">
        <ElIcon><ArrowRight /></ElIcon>
      </ElButton>
    </div>
  </ElDialog>
</template>

<style lang="less">
.hrt-img-preview {
  .el-dialog {
    height: v-bind(dialogHeight);
    padding: 0;
    display: flex;
    flex-direction: column;
    .el-dialog__header {
      padding: 0 16px;
      height: 48px;
      line-height: 48px;
      border-bottom: 1px solid var(--el-border-color);
    }
    .el-dialog__title {
      font-size: 16px;
      font-weight: 600;
    }
    .el-dialog__body {
      flex: auto;
      display: flex;
      flex-direction: column;
      .hrt-img-preview-btns {
        flex: none;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: 14px 16px;
        .el-button {
          padding: 0;
          border: none;
        }
      }
      .hrt-img-preview-btns-divider {
        width: 1px;
        height: 12px;
        background-color: var(--el-border-color);
        margin: 0 16px;
      }
      .hrt-img-preview-content {
        position: relative;
        flex: auto;
        height: 0;
        overflow: hidden;
        .el-button {
          width: 28px;
          height: 28px;
          border-radius: 50%;
          background-color: #ffffff;
          box-shadow: 0px 2px 8px 0px rgba(200, 201, 204, 0.5);
          font-size: 16px;
          position: absolute;
          top: 50%;
          transform: translate(0, -50%);
          z-index: 2;
        }
        .hrt-img-preview-prev {
          left: 16px;
        }
        .hrt-img-preview-next {
          right: 16px;
        }
      }
      .hrt-img-preview-img {
        max-width: 100%;
        max-height: 100%;
        position: absolute;
        left: 50%;
        top: 50%;
        user-select: none;
      }
    }
  }
}
</style>
