<script setup lang="ts">
import { ElInputNumber } from 'element-plus'

defineOptions({
  name: 'HrtInputNumber',
})

const {
  controlsPosition = 'right',
  min = 0,
  max,
  step,
  precision,
  stepStrictly = false,
  readonly = false,
  name,
} = defineProps<HrtInputNumberProps>()

const emit = defineEmits<{
  (e: 'change', value?: number, oldValue?: number): void
  (e: 'blur', event: FocusEvent): void
  (e: 'focus', event: FocusEvent): void
}>()

export interface HrtInputNumberProps {
  /** 控制按钮位置 */
  controlsPosition?: 'right' | 'between'
  /** 最小值 */
  min?: number
  /** 最大值 */
  max?: number
  /** 步长 */
  step?: number
  /** 精度 */
  precision?: number
  /** 严格步进 */
  stepStrictly?: boolean
  /** 是否只读 */
  readonly?: boolean
  /** 名称 */
  name?: string
}
</script>

<template>
  <ElInputNumber
    class="hrt-input-number"
    v-bind="$attrs"
    :controls-position="controlsPosition === 'right' ? 'right' : ''"
    :min="min"
    :max="max"
    :step="step"
    :precision="precision"
    :step-strictly="stepStrictly"
    :readonly="readonly"
    :name="name"
    @change="(value, oldValue) => emit('change', value, oldValue)"
    @blur="emit('blur', $event)"
    @focus="emit('focus', $event)"
  />
</template>

<style lang="css">
.hrt-input-number {
  width: 160px !important;
  .el-input-number__decrease,
  .el-input-number__increase {
    background-color: var(--hrt-color-neutral-700);
    width: 30px;
    color: var(--hrt-color-neutral-300);
    &.is-disabled {
      color: var(--hrt-color-neutral-400);
    }
  }
  .el-input__wrapper {
    .el-input__inner {
      text-align: start;
      height: 32px;
    }
  }

  &.is-controls-right {
    .el-input {
      .el-input__wrapper {
        padding-left: 12px;
        padding-right: 37px;
      }
    }

    .el-input-number__decrease,
    .el-input-number__increase {
      background-color: unset;
      width: 25px;
    }
  }
}
</style>
