<script setup lang="ts">
import type { RadioProps } from 'element-plus'
import { ElRadio } from 'element-plus'
import { useAttrs } from 'vue'

export interface HrtRadioProps extends Partial<RadioProps> {
}

defineOptions({
  name: 'HrtRadio',
})

const props = defineProps<HrtRadioProps>()

const emit = defineEmits<{
  change: [value: string | number | boolean]
}>()
const attrs = useAttrs()
</script>

<template>
  <ElRadio
    v-bind="{ ...props, ...attrs }"
    class="hrt-radio"
    @change="emit('change', $event as string | number | boolean)"
  >
    <slot />
  </ElRadio>
</template>

<style lang="css">
.el-radio {
  margin-right: 32px;
}

.el-radio__input.is-checked + .el-radio__label {
  color: var(--hrt-color-neutral-200) !important;
}

.el-radio__input.is-checked .el-radio__inner:after {
  background-color: var(--hrt-color-blue);
  height: 8px;
  width: 8px;
}

.el-radio__input.is-checked .el-radio__inner {
  border-color: var(--hrt-color-blue);
  background-color: transparent !important;
}
</style>
