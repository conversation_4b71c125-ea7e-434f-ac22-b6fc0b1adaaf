<script setup lang="ts">
import type { RadioButtonProps } from 'element-plus'
import { ElRadioButton } from 'element-plus'
import { useAttrs } from 'vue'

export interface HrtRadioProps extends Partial<RadioButtonProps> {
  selectedType?: 'fill' | 'outline'
}

defineOptions({
  name: 'HrtRadioButton',
})

const { selectedType = 'fill', ...restProps } = defineProps<HrtRadioProps>()

const emit = defineEmits<{
  change: [value: string | number | boolean]
}>()
const attrs = useAttrs()
</script>

<template>
  <ElRadioButton
    v-bind="{ ...restProps, ...attrs }"
    class="hrt-radio-button"
    :class="{
      'hrt-radio-button-fill': selectedType === 'fill',
      'hrt-radio-button-outline': selectedType === 'outline',
    }"
    @change="emit('change', $event as string | number | boolean)"
  >
    <slot />
  </ElRadioButton>
</template>

<style lang="css">
.hrt-radio-button-outline.is-active {
  .el-radio-button__original-radio:not(:disabled) + .el-radio-button__inner {
    background-color: #e6eeff !important;
    border-color: var(--hrt-color-blue);
    color: var(--hrt-color-blue) !important;
  }
}
.el-radio {
  margin-right: 32px;
}

.el-radio__input.is-checked + .el-radio__label {
  color: var(--hrt-color-neutral-200) !important;
}

.el-radio__input.is-checked .el-radio__inner:after {
  background-color: var(--hrt-color-blue);
  height: 8px;
  width: 8px;
}

.el-radio__input.is-checked .el-radio__inner {
  border-color: var(--hrt-color-blue);
  background-color: transparent !important;
}

.el-radio-button__original-radio:disabled:checked + .el-radio-button__inner {
  --el-radio-button-disabled-checked-fill: var(--hrt-color-neutral-400);
  color: white;
}

.el-radio-button__original-radio:disabled + .el-radio-button__inner {
  --el-button-disabled-bg-color: var(--hrt-color-neutral-700);
  color: var(--hrt-color-neutral-400);
}
</style>
