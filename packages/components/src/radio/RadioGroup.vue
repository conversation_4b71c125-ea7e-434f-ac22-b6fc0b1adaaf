<script setup lang="ts">
import type { RadioGroupProps } from 'element-plus'
import { ElRadioGroup } from 'element-plus'
import { useAttrs } from 'vue'

export interface HrtRadioProps extends Partial<RadioGroupProps> {
  direction?: 'horizontal' | 'vertical'
}

defineOptions({
  name: 'HrtRadioGroup',
})

const { direction = 'horizontal', ...restProps } = defineProps<HrtRadioProps>()

const emit = defineEmits<{
  change: [value: string | number | boolean]
}>()
const attrs = useAttrs()
</script>

<template>
  <ElRadioGroup
    v-bind="{ ...restProps, ...attrs }"
    class="hrt-radio-group"
    :class="{
      'hrt-radio-group-vertical': direction === 'vertical',
    }"
    @change="emit('change', $event as string | number | boolean)"
  >
    <slot />
  </ElRadioGroup>
</template>

<style lang="css">
.hrt-radio-group-vertical {
  display: flex;
  flex-direction: column;
  align-items: flex-start !important;
  gap: 0px;
  .el-radio {
    margin-right: 0;
  }
}
</style>
