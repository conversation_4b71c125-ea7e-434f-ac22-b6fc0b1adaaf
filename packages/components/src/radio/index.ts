import type { App } from 'vue'
import Radio from './Radio.vue'
import RadioButton from './RadioButton.vue'
import RadioGroup from './RadioGroup.vue'

export type { HrtRadioProps } from './Radio.vue'

Radio.install = (app: App) => {
  app.component(Radio.name!, Radio)
}

RadioGroup.install = (app: App) => {
  app.component(RadioGroup.name!, RadioGroup)
}
RadioButton.install = (app: App) => {
  app.component(RadioButton.name!, RadioButton)
}

export const HrtRadio = Radio
export const HrtRadioGroup = RadioGroup
export const HrtRadioButton = RadioButton
