<script setup lang="ts">
import { provide } from 'vue'

export interface RowProps {
  /** 对齐方式 */
  align?: 'top' | 'middle' | 'bottom' | 'stretch'
  /** 栅格间隔 */
  gutter?: number
  /** 水平排列方式 */
  justify?:
    | 'start'
    | 'end'
    | 'center'
    | 'space-around'
    | 'space-between'
    | 'space-evenly'
}

defineOptions({
  name: 'HrtRow',
})

const {
  gutter = 16,
  align = 'top',
  justify = 'start',
} = defineProps<RowProps>()

provide('gutter', gutter)
</script>

<template>
  <div
    class="hrt-flex hrt-flex-wrap"
    :style="{
      alignItems: align,
      justifyContent: justify,
      marginLeft: `-${gutter / 2}px`,
      marginRight: `-${gutter / 2}px`,
      rowGap: `${gutter}px`,
    }"
  >
    <slot />
  </div>
</template>
