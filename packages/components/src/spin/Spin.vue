<script setup lang="ts">
defineOptions({
  name: 'HrtSpin',
})

const {
  size = 'medium',
  direction = 'vertical',
  text = '加载中...',
} = defineProps<HrtSpinProps>()

export interface HrtSpinProps {
  /** 加载图标尺寸 */
  size?: 'small' | 'medium' | 'large'
  /** 排列方向 */
  direction?: 'horizontal' | 'vertical'
  /** 加载文案 */
  text?: string
}
</script>

<template>
  <div class="hrt-spin" :class="[`hrt-spin-${size}`, `hrt-spin-${direction}`]">
    <div class="hrt-spin-icon" />
    <div v-if="text" class="hrt-spin__text">
      {{ text }}
    </div>
  </div>
</template>

<style lang="css">
.hrt-spin {
  display: inline-flex;
  align-items: center;
  gap: 8px;

  .hrt-spin-icon {
    border-radius: 50%;
    border: 2px solid var(--hrt-color-neutral-500);
    border-top-color: var(--hrt-color-blue);
    animation: hrt-spin-rotate 1s linear infinite;
  }

  .hrt-spin__text {
    color: var(--hrt-color-blue-400);
    font-size: 14px;
  }

  &.hrt-spin-small {
    .hrt-spin-icon {
      width: 16px;
      height: 16px;
    }
  }

  &.hrt-spin-medium {
    .hrt-spin-icon {
      width: 24px;
      height: 24px;
    }
  }

  &.hrt-spin-large {
    .hrt-spin-icon {
      width: 32px;
      height: 32px;
    }
  }
}

.hrt-spin-vertical {
  flex-direction: column;
}

.hrt-spin-horizontal {
  flex-direction: row;
}

@keyframes hrt-spin-rotate {
  to {
    transform: rotate(360deg);
  }
}
</style>
