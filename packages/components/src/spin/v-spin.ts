import type { DirectiveBinding } from 'vue'
import { createApp } from 'vue'
import Spin from './Spin.vue'

interface SpinOptions {
  text?: string
  size?: 'small' | 'medium' | 'large'
  direction?: 'horizontal' | 'vertical'
  show?: boolean
}

type SpinBinding = boolean | SpinOptions

const SPIN_APP_KEY = '__hrt_spin_app__'
const SPIN_EL_KEY = '__hrt_spin_el__'

function createSpinEl() {
  const container = document.createElement('div')
  container.style.position = 'absolute'
  container.style.top = '0'
  container.style.left = '0'
  container.style.width = '100%'
  container.style.height = '100%'
  container.style.display = 'flex'
  container.style.justifyContent = 'center'
  container.style.alignItems = 'center'
  container.style.background = 'rgba(0, 0, 0, 0.2)'
  container.style.zIndex = '9999'
  return container
}

const vSpin = {
  mounted(el: HTMLElement, binding: DirectiveBinding<SpinBinding>) {
    const show = typeof binding.value === 'boolean'
      ? binding.value
      : binding.value.show
    if (show) {
      const options = typeof binding.value === 'object' ? binding.value : {}
      const app = createApp(Spin, { ...options })
      const spinEl = createSpinEl()
      el.style.position = 'relative'
      el.style.overflow = 'hidden'
      app.mount(spinEl)
      el.appendChild(spinEl)
      ;(el as any)[SPIN_APP_KEY] = app
      ;(el as any)[SPIN_EL_KEY] = spinEl
    }
  },
  updated(el: HTMLElement, binding: DirectiveBinding<SpinBinding>) {
    const app = (el as any)[SPIN_APP_KEY]
    const spinEl = (el as any)[SPIN_EL_KEY]
    const show = typeof binding.value === 'boolean'
      ? binding.value
      : binding.value.show
    if (show) {
      if (!app) {
        // 新增
        const options = typeof binding.value === 'object' ? binding.value : {}
        const newApp = createApp(Spin, { ...options })
        const newSpinEl = createSpinEl()
        el.style.position = 'relative'
        el.style.overflow = 'hidden'
        newApp.mount(newSpinEl)
        el.appendChild(newSpinEl)
        ;(el as any)[SPIN_APP_KEY] = newApp
        ;(el as any)[SPIN_EL_KEY] = newSpinEl
      }
    }
    else {
      // 移除
      if (app && spinEl) {
        app.unmount()
        el.removeChild(spinEl)
        delete (el as any)[SPIN_APP_KEY]
        delete (el as any)[SPIN_EL_KEY]
      }
    }
  },
  unmounted(el: HTMLElement) {
    const app = (el as any)[SPIN_APP_KEY]
    const spinEl = (el as any)[SPIN_EL_KEY]
    if (app && spinEl) {
      app.unmount()
      el.removeChild(spinEl)
      delete (el as any)[SPIN_APP_KEY]
      delete (el as any)[SPIN_EL_KEY]
    }
  },
}

export default vSpin
