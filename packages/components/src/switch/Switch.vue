<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: [<PERSON>olean, String, Number],
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  size: {
    type: String,
    default: 'default', // 可选 large, default, small
    validator: (val: string) => ['large', 'default', 'small'].includes(val),
  },
  activeText: String,
  inactiveText: String,
  activeValue: {
    type: [Boolean, String, Number],
    default: true,
  },
  inactiveValue: {
    type: [Boolean, String, Number],
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'change'])

const isChecked = computed(() => props.modelValue === props.activeValue)

function toggleSwitch() {
  if (props.disabled)
    return
  const newValue = isChecked.value ? props.inactiveValue : props.activeValue
  emit('update:modelValue', newValue)
  emit('change', newValue)
}
</script>

<template>
  <div
    class="hrt-switch" :class="[
      `hrt-switch--${size}`,
      { 'is-checked': isChecked, 'is-disabled': disabled },
    ]"
    @click="toggleSwitch"
  >
    <input type="checkbox" class="hrt-switch__input" :checked="isChecked" :disabled="disabled">
    <div class="hrt-switch__core">
      <span v-if="isChecked && activeText" class="hrt-switch__label hrt-switch__label--active">{{ activeText }}</span>
      <span v-if="!isChecked && inactiveText" class="hrt-switch__label hrt-switch__label--inactive">{{ inactiveText }}</span>
      <span class="hrt-switch__button" />
    </div>
  </div>
</template>

<style lang="css">
.hrt-switch {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.hrt-switch__input {
  position: absolute;
  width: 0;
  height: 0;
  opacity: 0;
  margin: 0;
}
.hrt-switch.is-disabled {
  cursor: not-allowed;
  .hrt-switch__core {
    background: var(--hrt-color-neutral-500);
  }
}
.hrt-switch__core {
  position: relative;
  display: flex;
  align-items: center;
  min-width: 34px;
  height: 18px;
  background: var(--hrt-color-neutral-400);
  border-radius: 9px;
  transition: background 0.2s;
}
.hrt-switch--large .hrt-switch__core {
  min-width: 42px;
  height: 22px;
  border-radius: 11px;
}
.hrt-switch--small .hrt-switch__core {
  min-width: 22px;
  height: 14px;
  border-radius: 7px;
}
.hrt-switch.is-checked {
  .hrt-switch__core {
    background: var(--hrt-color-blue);
  }

  &.is-disabled {
    .hrt-switch__core {
      background: var(--hrt-color-blue-200);
    }
  }
}
.hrt-switch__button {
  position: absolute;
  left: 2px;
  top: 2px;
  width: 14px;
  height: 14px;
  background: #fff;
  border-radius: 50%;
  transition: left 0.2s;
}
.hrt-switch--large {
  .hrt-switch__button {
    width: 18px;
    height: 18px;
  }
  .hrt-switch__label {
    font-size: 12px;
  }
  .hrt-switch__label--active {
    margin-left: 6px;
  }
}
.hrt-switch--small {
  .hrt-switch__button {
    width: 10px;
    height: 10px;
  }
  .hrt-switch__label {
    font-size: 6px;
    line-height: 10px;
  }
  .hrt-switch__label--inactive {
    margin-right: 2px;
  }
}
.hrt-switch.is-checked .hrt-switch__button {
  left: calc(100% - 16px);
}
.hrt-switch--large.is-checked .hrt-switch__button {
  left: calc(100% - 20px);
}
.hrt-switch--small.is-checked .hrt-switch__button {
  left: calc(100% - 12px);
}
.hrt-switch__label {
  font-size: 10px;
  color: #909399;
  user-select: none;
}
.hrt-switch__label--active {
  color: white;
  margin-left: 4px;
}
.hrt-switch__label--inactive {
  color: white;
  margin-left: auto;
  text-align: right;
  margin-right: 4px;
}
</style>
