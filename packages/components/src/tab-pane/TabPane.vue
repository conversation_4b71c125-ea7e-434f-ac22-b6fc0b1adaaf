<script setup lang="ts">
import type { Component } from 'vue'
import { eagerComputed } from '@vueuse/core'
import { computed, inject, onUnmounted, reactive, useId, useSlots } from 'vue'
import { tabsRootContextKey } from '../tabs/constants'

defineOptions({
  name: 'HrtTabPane',
})

const props = defineProps<HrtTabPaneProps>()

export interface HrtTabPaneProps {
  label: string
  name: string
  disabled?: boolean
  closable?: boolean
  icon?: Component
}
const slots = useSlots()
const id = useId()
const tabsRoot = inject(tabsRootContextKey)

const paneName = computed(() => props.name ?? id)
const isClosable = computed(() => props.closable ?? tabsRoot?.props.closable ?? false)
const active = eagerComputed(
  () => tabsRoot?.currentName.value === (props.name ?? id),
)

const pane = reactive({
  uid: id,
  slots,
  props,
  paneName,
  active,
  index: id,
  isClosable,
})
tabsRoot?.registerPane(pane)

onUnmounted(() => {
  tabsRoot?.unregisterPane(pane.uid)
})
</script>

<template>
  <div>
    <slot />
  </div>
</template>
