<script setup lang="ts">
import { ElTable, ElTableColumn } from 'element-plus'
import { computed, ref, watch } from 'vue'
import { HrtCheckbox } from '../checkbox'

defineOptions({
  name: 'HrtTable',
})

const { stripe = false, selection = false, data = [] } = defineProps<HrtTableProps>()
const emit = defineEmits<{
  selectionChange: [data: any[]]
}>()

export interface HrtTableProps {
  stripe?: boolean
  selection?: boolean
  data?: any[]
}

const selected = ref<any[]>(Array.from({ length: data.length }))
const allSelected = ref(false)
const allSelectedIndeterminate = ref(false)

const selectedData = computed(() => {
  return data.filter((_, index) => selected.value[index])
})

function handleAllSelected(value: string | number | boolean) {
  selected.value = Array.from({ length: data.length }, () => value)
}

watch(selected, (newVal) => {
  if (newVal.every(item => item)) {
    allSelected.value = true
    allSelectedIndeterminate.value = false
  }
  else if (newVal.some(item => item)) {
    allSelected.value = false
    allSelectedIndeterminate.value = true
  }
  else {
    allSelected.value = false
    allSelectedIndeterminate.value = false
  }
}, { immediate: true, deep: true })
</script>

<template>
  <div>
    <ElTable
      header-row-class-name="hrt-table-header-row"
      v-bind="$attrs"
      :stripe="stripe"
      :data="data"
      class="hrt-table-wrapper"
      @selection-change="emit('selectionChange', $event)"
    >
      <ElTableColumn v-if="selection" width="48">
        <template #default="{ $index }">
          <HrtCheckbox v-model="selected[$index]" />
        </template>
      </ElTableColumn>
      <slot />
    </ElTable>
    <div v-if="selection" class="hrt-table__footer">
      <div class="hrt-w-12 hrt-min-h-12 hrt-flex hrt-items-center hrt-justify-center">
        <HrtCheckbox
          v-model="allSelected"
          :indeterminate="allSelectedIndeterminate"
          @change="handleAllSelected"
        />
      </div>
      <div class="hrt-flex-1">
        <slot name="footer" :selected-data="selectedData" />
      </div>
    </div>
  </div>
</template>

<style lang="css">
.hrt-table-wrapper {
  --el-table-border: none;
  --el-table-header-bg-color: var(--hrt-neutral-700);
  --el-bg-color: var(--hrt-color-neutral-700);
  --el-fill-color-light: var(--hrt-color-blue-100);
  --el-bg-color: white;

  .sort-caret.ascending {
    --el-text-color-placeholder: var(--hrt-color-neutral-400);
  }
  .el-table__cell {
    padding: 14.5px 0;
  }

  .hrt-table-header-row {
    background-color: var(--hrt-neutral-700);
    box-shadow: 0px 1px 0px 0px #ebedf0;
    .el-table__cell {
      padding: 16px 0 15px;
      background-color: var(--hrt-color-neutral-700);
      .cell {
        line-height: 20px;
        font-size: 14px;
        color: var(--hrt-color-neutral-100);
        font-weight: 500;
      }
    }
  }

  .el-table__body {
    .el-table__cell {
      padding: 16px 0 15px;
      .cell {
        line-height: 20px;
      }
    }
  }
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: transparent;
    &::before {
      background-color: var(--hrt-color-blue);
    }
  }
  .el-checkbox__input.is-disabled .el-checkbox__inner {
    background-color: var(--hrt-color-neutral-700);
  }
  &.is-scrolling-left th.el-table-fixed-column--left,
  &.is-scrolling-right th.el-table-fixed-column--right {
    background-color: var(--hrt-color-neutral-700) !important;
  }
}

.hrt-table__footer {
  display: flex;
  align-items: center;
}
</style>
