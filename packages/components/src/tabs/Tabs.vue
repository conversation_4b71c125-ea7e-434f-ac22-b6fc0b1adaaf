<script setup lang="ts">
import type { TabsPaneContext } from './constants'
import { ArrowLeftBold, ArrowRightBold } from '@element-plus/icons-vue'
import { ElIcon } from 'element-plus'
import { isEmpty } from 'lodash-es'
import { computed, provide, ref, useSlots, watch } from 'vue'
import { tabsRootContextKey } from './constants'

export interface HrtTabsProps {
  contentClass?: string
  type?: 'basic' | 'card'
  pageTurning?: boolean
  pageTurningNumber?: number
  closable?: boolean
}

defineOptions({
  name: 'HrtTabs',
})

const {
  contentClass,
  type = 'basic',
  pageTurning = false,
  pageTurningNumber = 3,
  closable,
} = defineProps<HrtTabsProps>()
const emit = defineEmits<{
  close: [name: string]
}>()
const slots = useSlots()
const active = defineModel<string>()
const paneList = ref<TabsPaneContext[]>([])

// 获取所有的 HrtTabPane 组件
const panes = computed(() => {
  const defaultSlot = slots.default?.() || []
  const templates = defaultSlot
    .filter(node => (node.type as any).toString().startsWith('Symbol(') && Array.isArray(node.children))
    .map(item => item.children)
  const templateChildren = templates.flat()
  return [...defaultSlot, ...templateChildren]
    .filter(node => (node as any)?.type?.name === 'HrtTabPane')
    .map(node => ({
      label: (node as any)?.props?.label || '',
      name: (node as any)?.props?.name || '',
      disabled: (node as any)?.props?.disabled || false,
      icon: (node as any)?.props?.icon || null,
      content: node,
      closeable: (node as any)?.props?.closeable ?? closable ?? false,
    }))
})

const activeTab = computed(() => {
  return panes.value.find(pane => pane.name === active.value)
})

const currentIndex = computed(() => {
  return panes.value.findIndex(pane => pane.name === active.value)
})

const disabledLeft = computed(() => {
  /** 第一个未被禁用的TabPane */
  const firstEnablePaneIndex = panes.value.findIndex(pane => !pane.disabled)
  return currentIndex.value === firstEnablePaneIndex
})
const disabledRight = computed(() => {
  /** 最后一个未被禁用的TabPane */
  const lastEnablePaneIndex = panes.value.findLastIndex(pane => !pane.disabled)
  return currentIndex.value === lastEnablePaneIndex
})

provide(tabsRootContextKey, {
  props: {
    contentClass,
    type,
    pageTurning,
    pageTurningNumber,
  },
  currentName: active.value,
  registerPane: (pane: TabsPaneContext) => {
    paneList.value.push(pane)
  },
  unregisterPane: (uid: string) => {
    paneList.value = paneList.value.filter(pane => pane.uid !== uid)
  },
})

watch(
  active,
  (val) => {
    if (!val && !isEmpty(panes.value)) {
      active.value = panes.value[0].name
    }
  },
  { immediate: true },
)

// 监听 panes 的变化
watch(
  panes,
  (newPanes) => {
    // 如果当前激活的标签不在新的 panes 列表中，则激活第一个可用的标签
    if (newPanes.length && !newPanes.some(pane => pane.name === active.value)) {
      const firstEnablePane = newPanes.find(pane => !pane.disabled)
      if (firstEnablePane) {
        active.value = firstEnablePane.name
      }
    }
  },
  { immediate: true },
)

function handleClick(name: string, disabled: boolean) {
  if (disabled)
    return
  active.value = name
}

function handlePagination(type: 'left' | 'right') {
  if (type === 'left' && disabledLeft.value)
    return
  if (type === 'right' && disabledRight.value)
    return
  if (type === 'left') {
    // 从当前索引往前找到前一个未被禁用的TabPane
    const prevIndex = panes.value.findLastIndex((pane, index) => {
      return index < currentIndex.value && !pane.disabled
    })
    active.value = panes.value[prevIndex].name
  }
  else {
    // 从当前索引往后找到后一个未被禁用的TabPane
    const nextIndex = panes.value.findIndex((pane, index) => {
      return index > currentIndex.value && !pane.disabled
    })
    active.value = panes.value[nextIndex].name
  }
}
</script>

<template>
  <div class="hrt-tabs">
    <ul
      class="hrt-tabs-nav" :class="{
        'is-card': type === 'card',
        'is-basic': type === 'basic',
      }"
    >
      <div v-if="pageTurning" class="hrt-tabs-nav__pagination" @click="handlePagination('left')">
        <ElIcon :color="disabledLeft ? 'var(--hrt-color-neutral-400)' : 'var(--hrt-color-neutral-100)'">
          <ArrowLeftBold />
        </ElIcon>
      </div>
      <span
        v-for="(pane) in panes"
        :key="pane.name"
        aria-current="page"
        class="hrt-tabs-nav-item"
        :class="{
          'hrt-tabs-nav-item__disabled': pane.disabled,
          'hrt-tabs-nav-item-active': active === pane.name,
        }"
        @click="handleClick(pane.name, pane.disabled)"
      >

        <slot
          v-if="slots.label"
          name="label"
          :pane="{
            label: pane.label,
            icon: pane.icon,
            disabled: pane.disabled,
            name: pane.name,
            active: active === pane.name,
            closeable: pane.closeable,
          }"
        />
        <template v-else>
          <component :is="pane.icon" v-if="pane.icon" />
          <span>{{ pane.label }}</span>
          <div
            v-if="pane.closeable"
            class="hrt-tabs-nav-item__close"
            @click.stop.prevent="emit('close', pane.name)"
          >
            <ElIcon>
              <Close />
            </ElIcon>
          </div>
        </template>
      </span>
      <div
        v-if="pageTurning"
        class="hrt-tabs-nav__pagination"
        @click="handlePagination('right')"
      >
        <ElIcon :color="disabledRight ? 'var(--hrt-color-neutral-400)' : 'var(--hrt-color-neutral-100)'">
          <ArrowRightBold />
        </ElIcon>
      </div>
    </ul>

    <div
      class="hrt-min-h-0 hrt-flex-1 hrt-overflow-y-hidden hrt-bg-white hrt-py-4"
      :class="contentClass"
      :style="{ height: 'calc(100% - 32px)' }"
    >
      <div class="hrt-relative hrt-h-full hrt-overflow-y-scroll">
        <keep-alive>
          <component :is="activeTab?.content" :key="activeTab?.name" />
        </keep-alive>
      </div>
    </div>
  </div>
</template>

<style lang="css">
.hrt-tabs {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.hrt-tabs-nav {
  z-index: 10;
  display: flex;
  flex-wrap: wrap;
  text-align: center;

  .hrt-tabs-nav-item {
    min-width: 80px;
    display: flex;
    cursor: pointer;
    place-content: center;
    align-items: center;
    padding-top: 10px;
    padding-bottom: 10px;
    color: var(--hrt-color-neutral-200);
    font-size: 14px;
    line-height: 20px;
    box-sizing: border-box;
    gap: 4px;
    position: relative;

    &.hrt-tabs-nav-item-active {
      color: var(--hrt-color-neutral-100);
      font-weight: 500;
    }
    &.hrt-tabs-nav-item__disabled {
      cursor: not-allowed;
      color: var(--hrt-color-neutral-400);
    }
    svg {
      width: 16px;
      height: 16px;
    }
  }

  &.is-basic {
    border-bottom-width: 1px;
    border-bottom-color: var(--hrt-color-neutral-500);

    .hrt-tabs-nav-item {
      border-bottom-color: transparent;
      border-bottom-width: 2px;
      border-bottom-style: solid;
      box-sizing: border-box;
      text-align: center;

      &.hrt-tabs-nav-item-active {
        border-bottom-color: var(--hrt-color-blue);
      }
    }
  }

  &.is-card {
    border-bottom-width: 1px;
    border-bottom-color: var(--hrt-color-neutral-500);
    box-sizing: border-box;

    .hrt-tabs-nav-item {
      border-color: var(--hrt-color-neutral-500);
      background-color: var(--hrt-color-neutral-700);
      min-width: 104px;
      border-width: 1px;
      margin-bottom: -1px;
      border-top-left-radius: 4px;

      & + .hrt-tabs-nav-item {
        border-left-width: 0;
        border-top-left-radius: 0;
      }
      &:last-child {
        border-top-right-radius: 4px;
      }

      &.hrt-tabs-nav-item-active {
        background-color: white;
        border-bottom-color: white;
        color: var(--hrt-color-blue);
      }
    }
  }
}

.hrt-tabs-nav__pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  width: 32px;
  cursor: pointer;
  user-select: none;
}

.hrt-tabs-nav-item__close {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  padding: 0 10px;
  align-items: center;
  color: var(--hrt-color-neutral-400);
  z-index: 10;
}
</style>
