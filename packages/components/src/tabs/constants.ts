import type { ComputedRef, Injection<PERSON><PERSON>, Ref, Slots, UnwrapRef } from 'vue'
import type { HrtTabPaneProps } from '../tab-pane/TabPane.vue'
import type { HrtTabsProps } from './Tabs.vue'

export interface TabsPaneContext {
  uid: string
  name: string
  label: string
  disabled: boolean
  closeable?: boolean
}

export interface TabsContext {
  props: {
    contentClass?: string
    type?: 'basic' | 'card'
    pageTurning?: boolean
    pageTurningNumber?: number
  }
  currentName: string | undefined
  registerPane: (pane: TabsPaneContext) => void
  unregisterPane: (uid: string) => void
}

export const tabsRootContextKey: InjectionKey<TabsContext> = Symbol('tabsRootContext')
