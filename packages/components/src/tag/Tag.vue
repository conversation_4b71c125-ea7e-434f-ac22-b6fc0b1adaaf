<script lang="ts" setup>
import type { inputProps, TagProps } from 'element-plus'
import type { ExtractPropTypes } from 'vue'
import { ElTag } from 'element-plus'
import { getCurrentInstance } from 'vue'

defineOptions({ name: 'HrtTag' })

// const props = defineProps({
//   ...tagProps,
//   prefixIcon: inputProps.prefixIcon,
//   suffixIcon: inputProps.suffixIcon,
// })
const props = defineProps<HrtTagProps>()

type HrtTagProps = Partial<TagProps> & {
  /** 前置图标 */
  prefixIcon?: ExtractPropTypes<typeof inputProps.prefixIcon>
  /** 后置图标 */
  suffixIcon?: ExtractPropTypes<typeof inputProps.suffixIcon>
}

const vm = getCurrentInstance()
function changeRef(el: any) {
  vm!.exposed = el || {}
  vm!.exposeProxy = el || {}
}
</script>

<template>
  <ElTag :ref="changeRef" class="hrt-tag" v-bind="{ ...$attrs, ...props }">
    <template v-for="(_, name) in $slots" :key="name" #[name]="scope">
      <template v-if="name === 'default'">
        <el-icon v-if="props.prefixIcon" class="hrt-tag-icon hrt-tag__prefix">
          <component :is="props.prefixIcon" />
        </el-icon>
        <slot v-bind="scope || {}" />
        <el-icon v-if="props.suffixIcon" class="hrt-tag-icon hrt-tag__suffix">
          <component :is="props.suffixIcon" />
        </el-icon>
      </template>
      <slot v-else :name="name" v-bind="scope || {}" />
    </template>
  </ElTag>
</template>

<style lang="less">
.hrt-tag.el-tag {
  border-radius: 2px;
  font-size: 14px;
  padding: 2px 4px;
  &.el-tag--large {
    padding: 4px 6px;
  }
  &.el-tag--small {
    padding: 0 4px;
    font-size: 12px;
  }
  &.is-round {
    padding: 4px 8px;
  }
  &.el-tag--primary {
    --el-tag-bg-color: #e6eeff;
    --el-tag-border-color: var(--el-color-primary);
    &.el-tag--dark {
      --el-tag-bg-color: var(--el-color-primary);
    }
  }
  &.el-tag--info {
    --el-tag-bg-color: #f7f8fa;
    --el-tag-border-color: #dcdfe6;
    --el-tag-text-color: #3a4762;
    &.el-tag--dark {
      --el-tag-bg-color: #f7f8fa;
      --el-tag-border-color: transparent;
    }
  }
  &.el-tag--success {
    --el-tag-bg-color: #e3f5e1;
    --el-tag-border-color: var(--el-color-success);
    &.el-tag--dark {
      --el-tag-bg-color: var(--el-color-success);
    }
  }
  &.el-tag--warning {
    --el-tag-bg-color: #fcede3;
    --el-tag-border-color: var(--el-color-warning);
    &.el-tag--dark {
      --el-tag-bg-color: var(--el-color-warning);
    }
  }
  &.el-tag--danger {
    --el-tag-bg-color: #ffe6e7;
    --el-tag-border-color: var(--el-color-danger);
    &.el-tag--dark {
      --el-tag-bg-color: var(--el-color-danger);
    }
  }
  &.el-tag--plain {
    --el-tag-bg-color: #fff;
  }

  &.is-closable .el-icon {
    font-size: 10px;
  }

  .hrt-tag-icon {
    display: inline-block;
    vertical-align: middle;
    font-size: 12px;
    margin-left: 2px;
    margin-right: 4px;
    cursor: default;
    &.hrt-tag__suffix {
      margin-left: 4px;
      margin-right: 2px;
    }
  }
}
</style>
