<script setup lang="ts">
export interface TextProps {
  /** 文本类型 */
  type?: 'success' | 'primary' | 'info' | 'warning' | 'danger' | 'default'
  /** 文本行数 */
  lineClamp?: number
  /** 是否截断 */
  truncated?: boolean
  /** 自定义元素标签 */
  tag?: string
}

defineOptions({
  name: 'HrtText',
})

const {
  type = 'default',
  lineClamp,
  truncated = false,
  tag = 'span',
} = defineProps<TextProps>()
</script>

<template>
  <component
    :is="tag"
    class="text-sm font-hrt"
    :class="{
      'text-neutral-200': type === 'default',
      'text-neutral-300': type === 'info',
      'text-blue': type === 'primary',
      'text-green': type === 'success',
      'text-orange': type === 'warning',
      'text-red': type === 'danger',
      'is-line-clamp overflow-hidden': lineClamp,
      'inline-block text-ellipsis whitespace-nowrap overflow-hidden max-w-full': truncated,
    }"
    :style="{
      '-webkit-line-clamp': lineClamp,
    }"
  >
    <slot />
  </component>
</template>

<style lang="css" scoped>
.is-line-clamp {
  display: -webkit-inline-box;
  -webkit-box-orient: vertical;
}
</style>
