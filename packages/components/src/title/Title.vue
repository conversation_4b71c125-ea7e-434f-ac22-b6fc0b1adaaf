<script setup lang="ts">
import { computed } from 'vue'

export interface TextProps {
  /** 文本类型 */
  type?: 'success' | 'primary' | 'info' | 'warning' | 'danger' | 'default'
  size?: 'base' | 'xl' | 'sm'
  /** 文本行数 */
  lineClamp?: number
  /** 是否截断 */
  truncated?: boolean
}

defineOptions({
  name: 'HrtTitle',
})

const {
  type = 'default',
  size = 'base',
  lineClamp,
  truncated = false,
} = defineProps<TextProps>()

const tag = computed(() => {
  if (size === 'xl')
    return 'h1'
  if (size === 'base')
    return 'h2'
  if (size === 'sm')
    return 'h3'
  return 'h2'
})
</script>

<template>
  <component
    :is="tag"
    class="font-medium m-0 font-hrt"
    :class="{
      'text-neutral-100': type === 'default',
      'text-neutral-300': type === 'info',
      'text-blue': type === 'primary',
      'text-green': type === 'success',
      'text-orange': type === 'warning',
      'text-red': type === 'danger',
      'text-xl': size === 'xl',
      'text-base': size === 'base',
      'text-sm': size === 'sm',
      'is-line-clamp overflow-hidden': lineClamp,
      'inline-block text-ellipsis whitespace-nowrap overflow-hidden max-w-full': truncated,
    }"
    :style="{
      '-webkit-line-clamp': lineClamp,
    }"
  >
    <slot />
  </component>
</template>

<style lang="css" scoped>
.is-line-clamp {
  display: -webkit-inline-box;
  -webkit-box-orient: vertical;
}
</style>
