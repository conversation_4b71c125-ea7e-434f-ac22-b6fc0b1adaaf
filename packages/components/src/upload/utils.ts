// 判断是否是url
export function isUrl(url: string): boolean {
  try {
    // eslint-disable-next-line no-new
    new window.URL(url)
    return true
  }
  catch {
    return false
  }
}

// 获取文件名称
export function getFileName(url?: string): string {
  if (!url)
    return ''

  let newUrl = url

  if (!isUrl(url)) {
    newUrl = `${location.origin}${url.startsWith('/') ? url : `/${url}`}`
  }

  return new window.URL(newUrl).pathname.split('/').pop() || ''
}

// 获取文件类型
export function getFileType(url?: string): string {
  if (!url)
    return ''

  const fileName = getFileName(url)

  return fileName.split('.').pop()!.toLowerCase() || ''
}
