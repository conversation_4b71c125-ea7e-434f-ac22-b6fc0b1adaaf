{"name": "@hrt/icons", "type": "module", "version": "0.0.1", "private": false, "main": "src/main.ts", "module": "src/main.ts", "files": ["dist/"], "scripts": {"dev": "vite", "build": "vite build", "publish": "pnpm publish --no-git-checks"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}, "publishConfig": {"access": "public", "main": "dist/main.js", "module": "dist/main.js"}}