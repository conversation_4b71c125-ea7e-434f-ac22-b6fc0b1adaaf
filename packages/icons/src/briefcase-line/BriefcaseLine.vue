<script setup lang="ts">
defineOptions({
  name: 'HrtBriefcaseLine',
})
</script>

<template>
  <svg
    width="18px"
    height="18px"
    viewBox="0 0 18 18"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
  >
    <g
      id="页面-1"
      stroke="none"
      stroke-width="1"
      fill="none"
      fill-rule="evenodd"
    >
      <g id="医院看板-架构" transform="translate(-24.000000, -169.000000)">
        <g id="编组-24" transform="translate(0.000000, 88.000000)">
          <g id="编组-13" transform="translate(0.000000, 30.000000)">
            <g id="briefcase-line" transform="translate(24.000000, 51.000000)">
              <polygon id="路径" points="0 0 18 0 18 18 0 18" />
              <rect
                id="矩形"
                fill="#D5DDF8"
                x="6"
                y="9"
                width="10"
                height="8"
                rx="1.2"
              />
              <path
                id="形状"
                d="M5.25,4.75 L5.25,2.5 C5.25,2.08578644 5.58578644,1.75 6,1.75 L12,1.75 C12.4142136,1.75 12.75,2.08578644 12.75,2.5 L12.75,4.75 L15.75,4.75 C16.1642136,4.75 16.5,5.08578644 16.5,5.5 L16.5,16 C16.5,16.4142136 16.1642136,16.75 15.75,16.75 L2.25,16.75 C1.83578644,16.75 1.5,16.4142136 1.5,16 L1.5,5.5 C1.5,5.08578644 1.83578644,4.75 2.25,4.75 L5.25,4.75 Z M3,13 L3,15.25 L15,15.25 L15,13 L3,13 Z M3,11.5 L15,11.5 L15,6.25 L3,6.25 L3,11.5 Z M6.75,3.25 L6.75,4.75 L11.25,4.75 L11.25,3.25 L6.75,3.25 Z"
                fill="#000000"
                fill-rule="nonzero"
              />
              <polygon
                id="路径"
                fill="#2E6BE6"
                fill-rule="nonzero"
                points="7.5461591 7.94740927 10.5461591 7.94740927 10.5461591 10.9474093 7.5461591 10.9474093"
              />
            </g>
          </g>
        </g>
      </g>
    </g>
  </svg>
</template>
