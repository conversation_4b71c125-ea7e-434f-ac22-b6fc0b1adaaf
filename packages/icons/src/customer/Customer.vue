<script lang="ts" setup>
defineOptions({
  name: 'Hrt<PERSON>ust<PERSON>',
})
</script>

<template>
  <svg width="19px" height="17px" viewBox="0 0 19 17">
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
      <g id="总览（全国）-327" transform="translate(-23.000000, -325.000000)">
        <g id="编组-24" transform="translate(0.000000, 88.000000)">
          <g id="编组-13" transform="translate(0.000000, 16.000000)">
            <g id="编组-60" transform="translate(23.000000, 220.000000)">
              <g id="编组-65" transform="translate(0.000000, 1.000000)">
                <path id="路径" d="M7.89820228,11.2142857 C7.89820228,12.9368163 8.81407024,14.5285001 10.3008076,15.3897654 C11.787545,16.2510307 13.619281,16.2510307 15.1060184,15.3897654 C16.5927557,14.5285001 17.5086237,12.9368163 17.5086237,11.2142857 C17.5086237,9.49175517 16.5927557,7.90007131 15.1060184,7.03880603 C13.619281,6.17754075 11.787545,6.17754075 10.3008076,7.03880603 C8.81407024,7.90007131 7.89820228,9.49175517 7.89820228,11.2142857 L7.89820228,11.2142857 Z" fill="#D5DDF8" fill-rule="nonzero" />
                <circle id="椭圆形" stroke="#000000" stroke-width="1.5" cx="9.5" cy="4.5" r="3.75" />
                <path id="路径" d="M11.4612362,13.0785714 L10.1878553,15.6439732 C9.90955354,16.2064732 9.11268943,16.2104911 8.82637896,15.6520089 L7.4849243,13.0303571 C7.3327593,12.7350446 7.38882009,12.3734375 7.62307411,12.1383929 L8.90246146,10.8546875 C9.20078496,10.5553571 9.6833082,10.5553571 9.9816317,10.8546875 L11.3170798,12.1946429 C11.5493317,12.4296875 11.6073947,12.7832589 11.4612362,13.0785714 L11.4612362,13.0785714 Z" fill="#2E6BE6" fill-rule="nonzero" />
                <path id="矩形" d="M13.2600083,9.89225816 C13.5554895,9.83249625 13.8778698,10.03314 14.1896681,10.2602606 C14.7196302,10.6462964 15.2220279,11.2299644 15.5696502,11.8037218 C16.0932578,12.6679463 16.578715,13.5458315 17.0260228,14.4374489 C17.1809424,14.7461848 17.1962085,15.0885427 17.095578,15.3925026 C16.9950145,15.6962599 16.7786822,15.9616754 16.4702742,16.1167582 C16.2955316,16.2046274 16.1026076,16.2502643 15.9060059,16.25 L2.90833976,16.25 C2.56316179,16.2500495 2.25064172,16.1101833 2.02440379,15.8840102 C1.79816585,15.6578371 1.65821004,15.3453572 1.65816056,15.0001792 C2.29621961,13.1961662 2.72385543,12.3042019 3.05254114,11.8634315 C3.48166054,11.2879786 4.00999787,10.6126329 4.58571844,10.1931296 C4.84847947,10.0016667 5.12126389,9.83813696 5.40773223,9.89351167 C6.31015332,10.0679508 6.93361881,10.4138479 7.47328056,10.7068771 C8.17943321,11.0903087 8.78146791,11.3801897 9.5,11.3801897 C10.3018072,11.3801897 10.7666497,11.102419 11.3369582,10.7192315 C11.7555724,10.4379667 12.2625958,10.0939876 13.2600083,9.89225816 Z" stroke="#000000" stroke-width="1.5" />
              </g>
            </g>
          </g>
        </g>
      </g>
    </g>
  </svg>
</template>
