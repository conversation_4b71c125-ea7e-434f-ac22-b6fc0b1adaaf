:root {
  /* Fog Pine Green */
  --hrt-color-fog-pine-green-100: #edfcff;
  --hrt-color-fog-pine-green-200: #8cd6e6;
  --hrt-color-fog-pine-green-300: #3bb4cc;
  --hrt-color-fog-pine-green-400: #18a3bf;
  --hrt-color-fog-pine-green-500: #10849c;
  --hrt-color-fog-pine-green-600: #086578;
  --hrt-color-fog-pine-green-700: #034754;
  --hrt-color-fog-pine-green: #18a3bf;

  /* Azure */
  --hrt-color-azure-100: #edf8ff;
  --hrt-color-azure-200: #95cdf5;
  --hrt-color-azure-300: #43a1e6;
  --hrt-color-azure-400: #1c8dde;
  --hrt-color-azure-500: #1271b5;
  --hrt-color-azure-600: #0a568c;
  --hrt-color-azure-700: #043c63;
  --hrt-color-azure: #1c8dde;

  /* Midnight Blue */
  --hrt-color-midnight-blue-100: #f2f3ff;
  --hrt-color-midnight-blue-200: #a8ade0;
  --hrt-color-midnight-blue-300: #6b72c2;
  --hrt-color-midnight-blue-400: #5059b3;
  --hrt-color-midnight-blue-500: #343c91;
  --hrt-color-midnight-blue-600: #1e2570;
  --hrt-color-midnight-blue-700: #0e1447;
  --hrt-color-midnight-blue: #5059b3;

  /* Brownish Yellow */
  --hrt-color-brownish-yellow-100: #fffbf2;
  --hrt-color-brownish-yellow-200: #f5d89f;
  --hrt-color-brownish-yellow-300: #ebb852;
  --hrt-color-brownish-yellow-400: #e5a82e;
  --hrt-color-brownish-yellow-500: #ba861e;
  --hrt-color-brownish-yellow-600: #8f6511;
  --hrt-color-brownish-yellow-700: #634508;
  --hrt-color-brownish-yellow: #e5a82e;

  /* Grass Green */
  --hrt-color-grass-green-100: #fcffde;
  --hrt-color-grass-green-200: #d6e68c;
  --hrt-color-grass-green-300: #b4cc3b;
  --hrt-color-grass-green-400: #a3bf18;
  --hrt-color-grass-green-500: #82990f;
  --hrt-color-grass-green-600: #617308;
  --hrt-color-grass-green-700: #404d03;
  --hrt-color-grass-green: #a3bf18;

  /* Blue */
  --hrt-color-blue-100: #e6eeff;
  --hrt-color-blue-200: #9fbcf5;
  --hrt-color-blue-300: #5285eb;
  --hrt-color-blue-400: #2e6be6;
  --hrt-color-blue-500: #1e52ba;
  --hrt-color-blue-600: #113b8f;
  --hrt-color-blue-700: #082663;
  --hrt-color-blue: #2e6be6;

  /* Red */
  --hrt-color-red-100: #fff2f3;
  --hrt-color-red-200: #f59fa6;
  --hrt-color-red-300: #eb525f;
  --hrt-color-red-400: #e52e3d;
  --hrt-color-red-500: #ba1e2b;
  --hrt-color-red-600: #8f111b;
  --hrt-color-red-700: #63080f;
  --hrt-color-red: #e52e3d;

  /* Orange */
  --hrt-color-orange-100: #fcf5f0;
  --hrt-color-orange-200: #f2be99;
  --hrt-color-orange-300: #e88b48;
  --hrt-color-orange-400: #e37221;
  --hrt-color-orange-500: #b85916;
  --hrt-color-orange-600: #8c420d;
  --hrt-color-orange-700: #612c06;
  --hrt-color-orange: #e37221;

  /* Yellow */
  --hrt-color-yellow-100: #fffceb;
  --hrt-color-yellow-200: #f5e69d;
  --hrt-color-yellow-300: #ebd254;
  --hrt-color-yellow-400: #e6c833;
  --hrt-color-yellow-500: #baa122;
  --hrt-color-yellow-600: #8f7a14;
  --hrt-color-yellow-700: #63550a;
  --hrt-color-yellow: #e6c833;

  /* Purple */
  --hrt-color-purple-100: #f8f2ff;
  --hrt-color-purple-200: #c39ff5;
  --hrt-color-purple-300: #9152e8;
  --hrt-color-purple-400: #7a2ee5;
  --hrt-color-purple-500: #5f1eba;
  --hrt-color-purple-600: #45118f;
  --hrt-color-purple-700: #2e0863;
  --hrt-color-purple: #7a2ee5;

  /* Pink */
  --hrt-color-pink-100: #ffebfc;
  --hrt-color-pink-200: #eb8ddb;
  --hrt-color-pink-300: #d63cbd;
  --hrt-color-pink-400: #cc19af;
  --hrt-color-pink-500: #a6118e;
  --hrt-color-pink-600: #800a6c;
  --hrt-color-pink-700: #59054c;
  --hrt-color-pink: #cc19af;

  /* Green */
  --hrt-color-green-100: #f3fff2;
  --hrt-color-green-200: #98e092;
  --hrt-color-green-300: #4ec244;
  --hrt-color-green-400: #2fb324;
  --hrt-color-green-500: #219117;
  --hrt-color-green-600: #15700d;
  --hrt-color-green-700: #0c4f06;
  --hrt-color-green: #2fb324;

  /* Neutral */
  --hrt-color-neutral-100: #1f2e4d;
  --hrt-color-neutral-200: #3d4b66;
  --hrt-color-neutral-300: #7a8599;
  --hrt-color-neutral-400: #b8becc;
  --hrt-color-neutral-500: #dcdfe6;
  --hrt-color-neutral-600: #e8eaed;
  --hrt-color-neutral-700: #f7f8fa;
  --hrt-color-neutral: #b8becc;

  /* Box Shadow */
  --hrt-shadow-sm: 0px 2px 4px 0px rgba(30, 82, 186, 0.1);
  --hrt-shadow-md: 0px 2px 16px 0px rgba(8, 38, 99, 0.2);
  --hrt-shadow-lg: 0px 4px 32px 0px rgba(8, 38, 99, 0.3);
  --hrt-shadow: 0px 2px 8px 0px rgba(8, 38, 99, 0.2);

  /* Font Family */
  --hrt-font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
