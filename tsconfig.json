{"compilerOptions": {"target": "esnext", "jsx": "preserve", "lib": ["esnext", "dom"], "useDefineForClassFields": true, "baseUrl": "./", "module": "esnext", "moduleResolution": "node", "paths": {}, "resolveJsonModule": true, "typeRoots": ["element-plus/types"], "strict": true, "sourceMap": true, "esModuleInterop": true}, "include": ["docs/**/*.ts", "docs/**/*.d.ts", "docs/**/*.tsx", "docs/**/*.vue", "packages/**/*.ts", "packages/**/*.d.ts", "packages/**/*.tsx", "packages/**/*.vue"]}